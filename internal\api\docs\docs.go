// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "NewsBalancer Support",
            "url": "https://github.com/alexand<PERSON>-<PERSON><PERSON><PERSON>/BalancedNewsGo",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/articles": {
            "get": {
                "description": "Fetches a list of articles with optional filters",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Articles"
                ],
                "summary": "Get articles",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by source (e.g., CNN, Fox)",
                        "name": "source",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by political leaning",
                        "name": "leaning",
                        "in": "query"
                    },
                    {
                        "maximum": 100,
                        "minimum": 1,
                        "type": "integer",
                        "default": 20,
                        "description": "Maximum number of articles to return",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "minimum": 0,
                        "type": "integer",
                        "default": 0,
                        "description": "Number of articles to skip",
                        "name": "offset",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/api.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/db.Article"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid parameters",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "Creates a new article with the provided information",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Articles"
                ],
                "summary": "Create article",
                "parameters": [
                    {
                        "description": "Article information",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.CreateArticleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Article created successfully",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/api.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.CreateArticleResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid request data",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Article URL already exists",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/articles/{id}": {
            "get": {
                "description": "Fetches a specific article by its ID with scores and metadata",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Articles"
                ],
                "summary": "Get article by ID",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "Article ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success with article details",
                        "schema": {
                            "$ref": "#/definitions/api.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid article ID",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Article not found",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/articles/{id}/bias": {
            "get": {
                "description": "Retrieves the political bias score and individual model results for an article",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Analysis"
                ],
                "summary": "Get article bias analysis",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "Article ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "maximum": 1,
                        "minimum": -1,
                        "type": "number",
                        "default": -1,
                        "description": "Minimum score filter",
                        "name": "min_score",
                        "in": "query"
                    },
                    {
                        "maximum": 1,
                        "minimum": -1,
                        "type": "number",
                        "default": 1,
                        "description": "Maximum score filter",
                        "name": "max_score",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "asc",
                            "desc"
                        ],
                        "type": "string",
                        "default": "desc",
                        "description": "Sort order (asc or desc)",
                        "name": "sort",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/api.StandardResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/api.ScoreResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Invalid parameters",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Article not found",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/articles/{id}/ensemble": {
            "get": {
                "description": "Retrieves individual model results and aggregation for an article's ensemble score",
                "tags": [
                    "Analysis"
                ],
                "summary": "Get ensemble scoring details",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "Article ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "Ensemble data not found",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/articles/{id}/summary": {
            "get": {
                "description": "Retrieves the text summary for an article",
                "tags": [
                    "Summary"
                ],
                "summary": "Get article summary",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "Article ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.StandardResponse"
                        }
                    },
                    "404": {
                        "description": "Summary not available",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/feedback": {
            "post": {
                "description": "Submit user feedback on an article's political bias analysis",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Feedback"
                ],
                "summary": "Submit user feedback",
                "parameters": [
                    {
                        "description": "Feedback information",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.FeedbackRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Feedback received",
                        "schema": {
                            "$ref": "#/definitions/api.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request data",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/feeds/healthz": {
            "get": {
                "description": "Returns the health of configured RSS feed sources",
                "tags": [
                    "Feeds"
                ],
                "summary": "Get feed health status",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "boolean"
                            }
                        }
                    }
                }
            }
        },
        "/api/llm/reanalyze/{id}": {
            "post": {
                "description": "Initiates a reanalysis of an article's political bias or directly updates the score",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Analysis"
                ],
                "summary": "Reanalyze article",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "Article ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Optional score to set directly",
                        "name": "request",
                        "in": "body",
                        "schema": {
                            "$ref": "#/definitions/api.ManualScoreRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success - reanalysis queued or score updated",
                        "schema": {
                            "$ref": "#/definitions/api.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid article ID or score",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Article not found",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "429": {
                        "description": "Rate limit exceeded",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error or LLM service unavailable",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/llm/score-progress/{id}": {
            "get": {
                "description": "Server-Sent Events endpoint streaming scoring progress for an article",
                "tags": [
                    "Analysis"
                ],
                "summary": "Score progress SSE stream",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "Article ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "event-stream",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/api/manual-score/{id}": {
            "post": {
                "description": "Updates an article's bias score manually",
                "tags": [
                    "Analysis"
                ],
                "summary": "Manually set article score",
                "parameters": [
                    {
                        "minimum": 1,
                        "type": "integer",
                        "description": "Article ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Score value between -1.0 and 1.0",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/api.ManualScoreRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/api.StandardResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/api.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/refresh": {
            "post": {
                "description": "Initiates a manual RSS feed refresh job",
                "tags": [
                    "Feeds"
                ],
                "summary": "Trigger RSS feed refresh",
                "responses": {
                    "200": {
                        "description": "Refresh started",
                        "schema": {
                            "$ref": "#/definitions/api.StandardResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "api.CreateArticleRequest": {
            "description": "Request body for creating a new article",
            "type": "object",
            "required": [
                "content",
                "pub_date",
                "source",
                "title",
                "url"
            ],
            "properties": {
                "content": {
                    "description": "Article content",
                    "type": "string",
                    "example": "Article content..."
                },
                "pub_date": {
                    "description": "Publication date in RFC3339 format",
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "source": {
                    "description": "News source name",
                    "type": "string",
                    "example": "CNN"
                },
                "title": {
                    "description": "Article title",
                    "type": "string",
                    "example": "Breaking News"
                },
                "url": {
                    "description": "Article URL",
                    "type": "string",
                    "example": "https://example.com/article"
                }
            }
        },
        "api.CreateArticleResponse": {
            "description": "Response from creating a new article",
            "type": "object",
            "properties": {
                "article_id": {
                    "description": "ID of the created article",
                    "type": "integer",
                    "example": 42
                },
                "status": {
                    "description": "Status of the operation",
                    "type": "string",
                    "example": "created"
                }
            }
        },
        "api.ErrorDetail": {
            "description": "Detailed error information",
            "type": "object",
            "properties": {
                "code": {
                    "description": "Error code",
                    "type": "string",
                    "example": "validation_error"
                },
                "message": {
                    "description": "Human-readable error message",
                    "type": "string",
                    "example": "Invalid input parameters"
                }
            }
        },
        "api.ErrorResponse": {
            "description": "Standard API error response",
            "type": "object",
            "properties": {
                "error": {
                    "description": "Error details",
                    "allOf": [
                        {
                            "$ref": "#/definitions/api.ErrorDetail"
                        }
                    ]
                },
                "success": {
                    "description": "Always false for errors",
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "api.FeedbackRequest": {
            "description": "Request body for submitting user feedback",
            "type": "object",
            "required": [
                "article_id",
                "feedback_text",
                "user_id"
            ],
            "properties": {
                "article_id": {
                    "description": "Article ID",
                    "type": "integer"
                },
                "category": {
                    "description": "Feedback category: agree, disagree, unclear, other",
                    "type": "string",
                    "example": "agree"
                },
                "ensemble_output_id": {
                    "description": "ID of specific ensemble output",
                    "type": "integer"
                },
                "feedback_text": {
                    "description": "Feedback content",
                    "type": "string"
                },
                "source": {
                    "description": "Source of the feedback",
                    "type": "string",
                    "example": "web"
                },
                "user_id": {
                    "description": "User ID",
                    "type": "string"
                }
            }
        },
        "api.IndividualScoreResult": {
            "description": "Individual model scoring result",
            "type": "object",
            "properties": {
                "confidence": {
                    "description": "Model confidence",
                    "type": "number",
                    "example": 0.8
                },
                "created_at": {
                    "description": "When the score was generated",
                    "type": "string"
                },
                "explanation": {
                    "description": "Explanation for the score",
                    "type": "string",
                    "example": "Reasoning"
                },
                "model": {
                    "description": "Model name",
                    "type": "string",
                    "example": "claude-3"
                },
                "score": {
                    "description": "Bias score",
                    "type": "number",
                    "example": 0.3
                }
            }
        },
        "api.ManualScoreRequest": {
            "description": "Request body for manually setting an article's bias score",
            "type": "object",
            "required": [
                "score"
            ],
            "properties": {
                "score": {
                    "description": "Score value between -1.0 and 1.0",
                    "type": "number",
                    "example": 0.5
                }
            }
        },
        "api.ScoreResponse": {
            "description": "Political bias score analysis result",
            "type": "object",
            "properties": {
                "composite_score": {
                    "description": "Overall bias score",
                    "type": "number",
                    "example": 0.25
                },
                "results": {
                    "description": "Individual model scores",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/api.IndividualScoreResult"
                    }
                },
                "status": {
                    "description": "Status message if applicable",
                    "type": "string",
                    "example": "scoring_unavailable"
                }
            }
        },
        "api.StandardResponse": {
            "description": "Standard API success response",
            "type": "object",
            "properties": {
                "data": {
                    "description": "Response data payload"
                },
                "success": {
                    "description": "Always true for success",
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "db.Article": {
            "type": "object",
            "properties": {
                "compositeScore": {
                    "type": "number"
                },
                "confidence": {
                    "type": "number"
                },
                "content": {
                    "type": "string"
                },
                "createdAt": {
                    "type": "string"
                },
                "escalated": {
                    "type": "boolean"
                },
                "failCount": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "lastAttempt": {
                    "type": "string"
                },
                "pubDate": {
                    "type": "string"
                },
                "scoreSource": {
                    "type": "string"
                },
                "source": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api",
	Schemes:          []string{"http"},
	Title:            "NewsBalancer API",
	Description:      "API for the NewsBalancer application which analyzes political bias in news articles",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
