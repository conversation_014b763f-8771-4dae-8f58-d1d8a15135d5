/* Utility Classes */
/* Small utility classes for common styling needs */

/* Text Alignment */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Load More Section */
.load-more-section {
  margin: var(--space-5, 1.25rem) 0;
  text-align: center;
}

/* Button Spacing */
.btn-group .btn {
  margin-right: var(--space-2-5, 0.625rem);
  margin-bottom: var(--space-2-5, 0.625rem);
}

.btn-group .btn:last-child {
  margin-right: 0;
}
