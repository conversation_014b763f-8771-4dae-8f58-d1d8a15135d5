{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/api/articles": {"get": {"description": "Fetches a list of articles with optional filtering by source, leaning, and pagination", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Articles"], "summary": "Get articles", "operationId": "getArticlesList", "parameters": [{"type": "string", "description": "Filter by news source", "name": "source", "in": "query"}, {"type": "string", "description": "Filter by political leaning (left/center/right)", "name": "leaning", "in": "query"}, {"minimum": 0, "type": "integer", "default": 0, "description": "Pagination offset", "name": "offset", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 20, "description": "Number of items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "List of articles", "schema": {"allOf": [{"$ref": "#/definitions/internal_api.StandardResponse"}, {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/internal_api.ArticleResponse"}}}}]}}, "500": {"description": "Server error", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}}}, "post": {"description": "Creates a new article with the provided information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Articles"], "summary": "Create article", "operationId": "createArticle", "parameters": [{"description": "Article information", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_api.CreateArticleRequest"}}], "responses": {"200": {"description": "Article created successfully", "schema": {"allOf": [{"$ref": "#/definitions/internal_api.StandardResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/internal_api.CreateArticleResponse"}}}]}}, "400": {"description": "Invalid request data", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "409": {"description": "Article URL already exists", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "500": {"description": "Server error", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}}}}, "/api/articles/{id}": {"get": {"description": "Fetches a specific article by its ID with scores and metadata", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Articles"], "summary": "Get article by ID", "operationId": "getArticleById", "parameters": [{"minimum": 1, "type": "integer", "description": "Article ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Success with article details", "schema": {"$ref": "#/definitions/internal_api.StandardResponse"}}, "400": {"description": "Invalid article ID", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "404": {"description": "Article not found", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "500": {"description": "Server error", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}}}}, "/api/articles/{id}/bias": {"get": {"description": "Retrieves the political bias score and individual model results for an article", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Analysis"], "summary": "Get article bias analysis", "operationId": "getArticleBias", "parameters": [{"minimum": 1, "type": "integer", "description": "Article ID", "name": "id", "in": "path", "required": true}, {"maximum": 1, "minimum": -1, "type": "number", "default": -1, "description": "Minimum score filter", "name": "min_score", "in": "query"}, {"maximum": 1, "minimum": -1, "type": "number", "default": 1, "description": "Maximum score filter", "name": "max_score", "in": "query"}, {"enum": ["asc", "desc"], "type": "string", "default": "desc", "description": "Sort order (asc or desc)", "name": "sort", "in": "query"}], "responses": {"200": {"description": "Success", "schema": {"allOf": [{"$ref": "#/definitions/internal_api.StandardResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/internal_api.ScoreResponse"}}}]}}, "400": {"description": "Invalid parameters", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "404": {"description": "Article not found", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "500": {"description": "Server error", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}}}}, "/api/articles/{id}/ensemble": {"get": {"description": "Retrieves individual model results and aggregation for an article's ensemble score", "tags": ["Analysis"], "summary": "Get ensemble scoring details", "operationId": "getArticleEnsemble", "parameters": [{"minimum": 1, "type": "integer", "description": "Article ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/internal_api.StandardResponse"}}, "404": {"description": "Ensemble data not found", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}}}}, "/api/feedback": {"post": {"description": "Submit user feedback on an article's political bias analysis", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["<PERSON><PERSON><PERSON>"], "summary": "Submit user feedback", "operationId": "submitFeedback", "parameters": [{"description": "Feedback information", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_api.FeedbackRequest"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> received", "schema": {"$ref": "#/definitions/internal_api.StandardResponse"}}, "400": {"description": "Invalid request data", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "500": {"description": "Server error", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}}}}, "/api/feeds/healthz": {"get": {"description": "Returns the health status of all configured RSS feeds", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Feeds"], "summary": "Get RSS feed health status", "operationId": "getFeedsHealth", "responses": {"200": {"description": "Feed health status mapping feed names to boolean status", "schema": {"type": "object", "additionalProperties": {"type": "boolean"}}}, "500": {"description": "Server error", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}}}}, "/api/llm/reanalyze/{id}": {"post": {"description": "Trigger a new LLM analysis for a specific article and update its scores.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["LLM"], "summary": "Reanalyze article", "operationId": "reanalyzeArticle", "parameters": [{"type": "integer", "description": "Article ID", "name": "id", "in": "path", "required": true}], "responses": {"202": {"description": "Reanalysis started", "schema": {"$ref": "#/definitions/internal_api.StandardResponse"}}, "400": {"description": "Invalid article ID", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "401": {"description": "LLM authentication failed", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "402": {"description": "LLM payment required or credits exhausted", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "404": {"description": "Article not found", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "429": {"description": "LLM rate limit exceeded", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "500": {"description": "Server error", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}, "503": {"description": "LLM service unavailable or streaming error", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}}}}, "/api/llm/score-progress/{id}": {"get": {"produces": ["text/event-stream"], "summary": "Stream LLM scoring progress", "operationId": "getScoreProgress", "parameters": [{"type": "integer", "description": "Article ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "SSE stream of progress updates", "schema": {"$ref": "#/definitions/github_com_alexand<PERSON>-sa<PERSON><PERSON>_BalancedNewsGo_internal_models.ProgressState"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/internal_api.StandardResponse"}}}}}, "/api/manual-score/{id}": {"post": {"description": "Updates an article's bias score manually", "tags": ["Analysis"], "summary": "Manually set article score", "operationId": "addManualScore", "parameters": [{"minimum": 1, "type": "integer", "description": "Article ID", "name": "id", "in": "path", "required": true}, {"description": "Score value between -1.0 and 1.0", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/internal_api.ManualScoreRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/internal_api.StandardResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}}}}, "/api/refresh": {"post": {"description": "Initiates a manual RSS feed refresh job to fetch new articles from configured RSS sources", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Feeds"], "summary": "Trigger RSS feed refresh", "operationId": "triggerRssRefresh", "responses": {"200": {"description": "Refresh started successfully", "schema": {"allOf": [{"$ref": "#/definitions/internal_api.StandardResponse"}, {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "string"}}}}]}}, "500": {"description": "Server error", "schema": {"$ref": "#/definitions/internal_api.ErrorResponse"}}}}}}, "definitions": {"github_com_alexandru-savinov_BalancedNewsGo_internal_models.ProgressState": {"description": "Progress state for long-running operations", "type": "object", "properties": {"error": {"description": "Error message if failed", "type": "string"}, "error_details": {"description": "Structured error details (JSON string)", "type": "string"}, "final_score": {"description": "Final score if completed", "type": "number", "example": 0.25}, "last_updated": {"description": "Timestamp", "type": "integer", "example": 1609459200}, "message": {"description": "User-friendly message", "type": "string", "example": "Processing article"}, "percent": {"description": "Progress percentage", "type": "integer", "example": 75}, "status": {"description": "Overall status", "type": "string", "example": "InProgress"}, "step": {"description": "Current detailed step", "type": "string", "example": "Scoring"}}}, "internal_api.ArticleResponse": {"type": "object", "properties": {"article_id": {"type": "integer"}, "composite_score": {"type": "number"}, "confidence": {"type": "number"}, "content": {"type": "string"}, "published_at": {"type": "string"}, "score_source": {"type": "string"}, "source": {"type": "string"}, "title": {"type": "string"}}}, "internal_api.CreateArticleRequest": {"description": "Request body for creating a new article", "type": "object", "required": ["content", "pub_date", "source", "title", "url"], "properties": {"content": {"description": "Article content", "type": "string", "example": "Article content..."}, "pub_date": {"description": "Publication date in RFC3339 format", "type": "string", "example": "2023-01-01T12:00:00Z"}, "source": {"description": "News source name", "type": "string", "example": "CNN"}, "title": {"description": "Article title", "type": "string", "example": "Breaking News"}, "url": {"description": "Article URL", "type": "string", "example": "https://example.com/article"}}}, "internal_api.CreateArticleResponse": {"description": "Response from creating a new article", "type": "object", "properties": {"article_id": {"description": "ID of the created article", "type": "integer", "example": 42}, "status": {"description": "Status of the operation", "type": "string", "example": "created"}}}, "internal_api.ErrorDetail": {"description": "Detailed error information", "type": "object", "properties": {"code": {"description": "Error code", "type": "string", "example": "validation_error"}, "message": {"description": "Human-readable error message", "type": "string", "example": "Invalid input parameters"}}}, "internal_api.ErrorResponse": {"description": "Standard API error response", "type": "object", "properties": {"error": {"description": "Error details", "allOf": [{"$ref": "#/definitions/internal_api.ErrorDetail"}]}, "success": {"description": "Always false for errors", "type": "boolean", "example": false}}}, "internal_api.FeedbackRequest": {"description": "Request body for submitting user feedback", "type": "object", "required": ["article_id", "feedback_text", "user_id"], "properties": {"article_id": {"description": "Article ID", "type": "integer"}, "category": {"description": "Feedback category: agree, disagree, unclear, other", "type": "string", "example": "agree"}, "ensemble_output_id": {"description": "ID of specific ensemble output", "type": "integer"}, "feedback_text": {"description": "Feedback content", "type": "string"}, "source": {"description": "Source of the feedback", "type": "string", "example": "web"}, "user_id": {"description": "User ID", "type": "string"}}}, "internal_api.IndividualScoreResult": {"description": "Individual model scoring result", "type": "object", "properties": {"confidence": {"description": "Model confidence", "type": "number", "example": 0.8}, "created_at": {"description": "When the score was generated", "type": "string"}, "explanation": {"description": "Explanation for the score", "type": "string", "example": "Reasoning"}, "model": {"description": "Model name", "type": "string", "example": "claude-3"}, "score": {"description": "Bias score", "type": "number", "example": 0.3}}}, "internal_api.ManualScoreRequest": {"description": "Request body for manually setting an article's bias score", "type": "object", "required": ["score"], "properties": {"score": {"description": "Score value between -1.0 and 1.0", "type": "number", "example": 0.5}}}, "internal_api.ScoreResponse": {"description": "Political bias score analysis result", "type": "object", "properties": {"composite_score": {"description": "Overall bias score", "type": "number", "example": 0.25}, "results": {"description": "Individual model scores", "type": "array", "items": {"$ref": "#/definitions/internal_api.IndividualScoreResult"}}, "status": {"description": "Status message if applicable", "type": "string", "example": "scoring_unavailable"}}}, "internal_api.StandardResponse": {"description": "Standard API success response", "type": "object", "properties": {"data": {"description": "Response data payload"}, "success": {"description": "Always true for success", "type": "boolean", "example": true}}}}}