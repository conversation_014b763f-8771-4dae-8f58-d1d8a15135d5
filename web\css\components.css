/* Reusable UI Components */
/* Styles for buttons, cards, navbars, forms, badges */

/* Navigation Bar Component */
.navbar {
  background-color: var(--color-bg, #fff);
  border-bottom: 1px solid var(--color-gray-300, #dee2e6);
  padding: var(--space-4, 1rem) 0;
  position: relative;
  z-index: 100;
}

.navbar .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--space-4, 1rem);
}

.navbar-brand {
  font-size: var(--font-size-xl, 1.25rem);
  font-weight: var(--font-weight-bold, 700);
  color: var(--color-text, #333);
  text-decoration: none;
  margin: 0;
}

.navbar-brand:hover {
  color: var(--color-primary, #007bff);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-6, 1.5rem);
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-nav a {
  color: var(--color-text, #333);
  text-decoration: none;
  font-weight: var(--font-weight-medium, 500);
  padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem);
  border-radius: var(--border-radius, 0.25rem);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.navbar-nav a:hover,
.navbar-nav a:focus {
  color: var(--color-primary, #007bff);
  background-color: var(--color-gray-100, #f8f9fa);
  text-decoration: none;
}

.navbar-nav a.active {
  color: var(--color-primary, #007bff);
  font-weight: var(--font-weight-semibold, 600);
}

/* Responsive navbar */
@media screen and (max-width: 768px) {
  .navbar .container {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3, 0.75rem);
  }

  .navbar-nav {
    width: 100%;
    justify-content: center;
    gap: var(--space-4, 1rem);
  }
}

/* Button Component */
.btn {
  display: inline-block;
  padding: var(--space-2-5, 0.625rem) var(--space-4, 1rem);
  margin: 0;
  font-family: inherit;
  font-size: var(--font-size-base, 1rem);
  font-weight: var(--font-weight-medium, 500);
  line-height: var(--line-height-base, 1.6);
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  background-color: var(--color-gray-100, #f8f9fa);
  border: var(--border-width, 1px) solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.25rem);
  color: var(--color-text, #333);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn:hover {
  color: var(--color-text, #333);
  background-color: var(--color-gray-200, #e9ecef);
  border-color: var(--color-gray-400, #ced4da);
  text-decoration: none;
}

.btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn:active {
  background-color: var(--color-gray-300, #dee2e6);
  border-color: var(--color-gray-400, #ced4da);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.btn:disabled,
.btn.disabled {
  opacity: 0.65;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button sizes */
.btn-sm {
  padding: var(--space-1-5, 0.375rem) var(--space-3, 0.75rem);
  font-size: var(--font-size-sm, 0.875rem);
  border-radius: var(--border-radius-sm, 0.125rem);
}

.btn-lg {
  padding: var(--space-3, 0.75rem) var(--space-5, 1.25rem);
  font-size: var(--font-size-lg, 1.125rem);
  border-radius: var(--border-radius-lg, 0.5rem);
}

/* Button Color Variants */
.btn-primary {
  color: var(--color-white, #fff);
  background-color: var(--color-primary, #007bff);
  border-color: var(--color-primary, #007bff);
}

.btn-primary:hover {
  color: var(--color-white, #fff);
  background-color: #0056b3;
  border-color: #004085;
}

.btn-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-primary:active {
  color: var(--color-white, #fff);
  background-color: #004085;
  border-color: #003d82;
}

.btn-secondary {
  color: var(--color-white, #fff);
  background-color: var(--color-gray-600, #6c757d);
  border-color: var(--color-gray-600, #6c757d);
}

.btn-secondary:hover {
  color: var(--color-white, #fff);
  background-color: #545b62;
  border-color: #4e555b;
}

.btn-secondary:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-secondary:active {
  color: var(--color-white, #fff);
  background-color: #4e555b;
  border-color: #47525d;
}

.btn-success {
  color: var(--color-white, #fff);
  background-color: var(--color-success, #28a745);
  border-color: var(--color-success, #28a745);
}

.btn-success:hover {
  color: var(--color-white, #fff);
  background-color: #218838;
  border-color: #1e7e34;
}

.btn-success:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-success:active {
  color: var(--color-white, #fff);
  background-color: #1e7e34;
  border-color: #1c7430;
}

.btn-warning {
  color: var(--color-gray-900, #212529);
  background-color: var(--color-warning, #ffc107);
  border-color: var(--color-warning, #ffc107);
}

.btn-warning:hover {
  color: var(--color-gray-900, #212529);
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-warning:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-warning:active {
  color: var(--color-gray-900, #212529);
  background-color: #d39e00;
  border-color: #c69500;
}

.btn-danger {
  color: var(--color-white, #fff);
  background-color: var(--color-danger, #dc3545);
  border-color: var(--color-danger, #dc3545);
}

.btn-danger:hover {
  color: var(--color-white, #fff);
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-danger:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-danger:active {
  color: var(--color-white, #fff);
  background-color: #bd2130;
  border-color: #b21f2d;
}

.btn-info {
  color: var(--color-white, #fff);
  background-color: var(--color-info, #17a2b8);
  border-color: var(--color-info, #17a2b8);
}

.btn-info:hover {
  color: var(--color-white, #fff);
  background-color: #138496;
  border-color: #117a8b;
}

.btn-info:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-info:active {
  color: var(--color-white, #fff);
  background-color: #117a8b;
  border-color: #10707f;
}

/* Enhanced Button Groups */
.btn-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--space-2);
  margin-top: var(--space-3);
}

.btn-group .btn {
  min-height: 44px;
  white-space: nowrap;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

@supports not (display: grid) {
  .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
  }

  .btn-group .btn {
    flex: 1 1 140px;
  }
}

@media screen and (max-width: 480px) {
  .btn-group {
    grid-template-columns: 1fr;
  }

  .btn-group .btn {
    min-height: 48px;
  }

  @supports not (display: grid) {
    .btn-group {
      flex-direction: column;
    }

    .btn-group .btn {
      flex: 1 1 auto;
    }
  }
}

/* Article Cards */
.article-item,
.article-card {
  background: var(--color-white, #fff);
  border: 1px solid var(--color-gray-300, rgba(210, 215, 217, 0.75));
  border-radius: var(--border-radius-md, 0.375rem);
  padding: var(--space-6, 1.5rem);
  transition: all 0.2s ease-in-out;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.article-item:hover,
.article-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary, #007bff);
}

.article-title {
  margin: 0 0 var(--space-2, 0.5rem) 0;
  font-size: var(--font-size-lg, 1.125rem);
  line-height: var(--line-height-snug, 1.375);
  font-weight: var(--font-weight-semibold, 600);
}

.article-title a {
  color: var(--color-text, #333);
  text-decoration: none;
}

.article-title a:hover {
  color: var(--color-primary, #007bff);
  text-decoration: underline;
}

.article-meta {
  color: var(--color-text-muted, #6c757d);
  font-size: var(--font-size-sm, 0.875rem);
  margin: var(--space-2, 0.5rem) 0;
  flex-grow: 1;
}

.article-actions {
  margin-top: auto;
  padding-top: var(--space-4, 1rem);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Bias Indicators */
.bias-indicator {
  display: inline-block;
  padding: var(--space-1, 0.25rem) var(--space-3, 0.75rem);
  border-radius: var(--border-radius-md, 0.375rem);
  font-size: var(--font-size-xs, 0.75rem);
  font-weight: var(--font-weight-semibold, 600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.bias-left {
  background-color: rgba(13, 110, 253, 0.1);
  color: var(--color-bias-left, #0d6efd);
  border: 1px solid rgba(13, 110, 253, 0.25);
}

.bias-center {
  background-color: rgba(108, 117, 125, 0.1);
  color: var(--color-bias-center, #6c757d);
  border: 1px solid rgba(108, 117, 125, 0.25);
}

.bias-right {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--color-bias-right, #dc3545);
  border: 1px solid rgba(220, 53, 69, 0.25);
}

/* Admin Control Section Cards */
.control-section {
  background: var(--color-bg-light, #f8f9fa);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius, 0.25rem);
  padding: var(--space-4, 1rem);
  height: fit-content;
}

.control-section h3 {
  margin-top: 0;
  margin-bottom: var(--space-3, 0.75rem);
  color: var(--color-text, #333);
}

/* Filter Section */
.filter-section {
  background: var(--color-gray-100, #f8f9fa);
  border: 1px solid var(--color-gray-300, #dee2e6);
  border-radius: var(--border-radius-md, 0.375rem);
  padding: var(--space-6, 1.5rem);
  margin-bottom: var(--space-8, 2rem);
}

/* Results Summary */
.results-summary {
  color: var(--color-text-muted, #6c757d);
  font-size: var(--font-size-sm, 0.875rem);
  margin: var(--space-4, 1rem) 0 var(--space-8, 2rem) 0;
  padding: var(--space-3, 0.75rem) var(--space-4, 1rem);
  background: rgba(248, 249, 250, 0.5);
  border-radius: var(--border-radius-md, 0.375rem);
  border-left: 3px solid var(--color-primary, #007bff);
}

/* HTMX Loading States */
.htmx-indicator {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.htmx-request .htmx-indicator {
  opacity: 1;
}

.htmx-request.htmx-indicator {
  opacity: 1;
}

/* Responsive adjustments */
@media screen and (max-width: 736px) {
  .filter-section {
    padding: var(--space-4, 1rem);
  }
}
