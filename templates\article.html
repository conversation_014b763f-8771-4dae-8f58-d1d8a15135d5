<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Article.Title}} - NewsBalancer</title>

    <!-- Unified CSS System -->
    <link rel="stylesheet" href="/static/css/app-consolidated.css?v=1" />


</head>
<body>
    <header class="navbar">
        <div class="container">
            <a href="/articles" class="navbar-brand">NewsBalancer</a>
            <nav class="navbar-nav">
                <a href="/articles">Articles</a>
                <a href="/admin">Admin</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="two-column-layout">
            <div>
                <h1>{{.Article.Title}}</h1>
                <div class="article-meta">
                    <div><strong>Source:</strong> {{.Article.Source}}</div>
                    <div><strong>Published:</strong> {{.Article.PubDate.Format "2006-01-02 15:04"}}</div>
                    <div><strong>Bias Score:</strong> {{if .Article.CompositeScore}}{{.Article.CompositeScore}}{{else}}N/A{{end}} (confidence: {{if .Article.Confidence}}{{.Article.Confidence}}{{else}}N/A{{end}}%)</div>
                </div>

                {{if .Article.Summary}}
                <div class="article-summary">
                    <strong>Summary:</strong> {{.Article.Summary}}
                </div>
                {{end}}
                
                <div class="bias-analysis">
                    <h2>Bias Analysis</h2>
                    <div class="bias-score" id="bias-score">Bias Analysis</div>                    <div id="bias-label-container">
                        <!-- Bias label section - currently not available in database -->
                        <div class="bias-label bias-unknown">Bias analysis pending</div>
                    </div>                      <div class="analysis-details" id="analysis-details">
                        <p>Detailed Confidence: <span id="confidence-value">{{if .Article.Confidence}}{{.Article.Confidence}}{{else}}N/A{{end}}</span>%</p>
                    </div>
                    
                    <!-- Progress Indicator for real-time updates -->
                    <progress-indicator
                        id="reanalysis-progress"
                        article-id="{{.Article.ID}}"
                        auto-connect="false"
                        show-details="true"
                        class="progress-hidden">
                    </progress-indicator>
                    
                    <button class="btn btn-primary" id="reanalyze-btn" data-article-id="{{.Article.ID}}">
                        <span id="btn-text">Request Reanalysis</span>
                        <span id="btn-loading" class="btn-loading-hidden">Processing...</span>
                    </button>
                </div>

                <div class="article-content">
                    {{.Article.Content}}
                </div>
                
                <div class="article-meta">
                    <div>Publication Date: {{.Article.PubDate.Format "2006-01-02 15:04"}}</div>
                    <div>Source: {{.Article.Source}}</div>
                    <div>Bias Score: {{if .Article.CompositeScore}}{{.Article.CompositeScore}}{{else}}N/A{{end}} (confidence: {{if .Article.Confidence}}{{.Article.Confidence}}{{else}}N/A{{end}}%)</div>
                </div>
            </div>

            <div class="sidebar">
                <div class="recent-articles">
                    <h3>Recent Articles</h3>                    {{range .RecentArticles}}
                    <div class="recent-article-item">
                        <a href="/article/{{.ID}}">{{.Title}}</a>
                        <div>
                            <!-- Bias labels not available from database -->
                            <small class="bias-unknown">Analysis pending</small>
                        </div>
                    </div>
                    {{end}}
                </div>

                <div class="stats">
                    <h3>Statistics</h3>
                    <div class="stats-item">
                        <span>Total Articles:</span>
                        <span>{{.Stats.TotalArticles}}</span>
                    </div>
                    <div class="stats-item">
                        <span>Left Leaning:</span>
                        <span>{{.Stats.LeftCount}} ({{.Stats.LeftPercentage}}%)</span>
                    </div>
                    <div class="stats-item">
                        <span>Center:</span>
                        <span>{{.Stats.CenterCount}} ({{.Stats.CenterPercentage}}%)</span>
                    </div>
                    <div class="stats-item">
                        <span>Right Leaning:</span>
                        <span>{{.Stats.RightCount}} ({{.Stats.RightPercentage}}%)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>    <!-- Import ProgressIndicator component and SSEClient utility -->
    <script type="module" src="/static/js/components/ProgressIndicator.js"></script>
    <script type="module" src="/static/js/utils/SSEClient.js"></script>
    
    <script>
        // Enhanced article detail page with real-time SSE progress tracking
        document.addEventListener('DOMContentLoaded', function() {
            const reanalyzeBtn = document.getElementById('reanalyze-btn');
            const btnText = document.getElementById('btn-text');
            const btnLoading = document.getElementById('btn-loading');
            const progressIndicator = document.getElementById('reanalysis-progress');

            // Elements for updating bias analysis results
            const biasScoreElement = document.getElementById('bias-score');
            
            if (reanalyzeBtn) {
                reanalyzeBtn.addEventListener('click', async function() {
                    console.log('🖱️ Reanalyze button clicked!');
                    const articleId = this.getAttribute('data-article-id');
                    console.log('📄 Article ID:', articleId);
                    
                    // Reset progress indicator to ensure clean state
                    if (progressIndicator && typeof progressIndicator.reset === 'function') {
                        progressIndicator.reset();
                    } else {
                        console.warn('ProgressIndicator reset method not available');
                    }

                    // Set up event handlers BEFORE connecting to SSE to prevent race conditions
                    if (progressIndicator) {
                        console.log('🎧 Setting up ProgressIndicator event listeners');

                        // Remove any existing listeners to prevent duplicates
                        if (progressIndicator._completedHandler) {
                            progressIndicator.removeEventListener('completed', progressIndicator._completedHandler);
                        }
                        if (progressIndicator._errorHandler) {
                            progressIndicator.removeEventListener('error', progressIndicator._errorHandler);
                        }
                        if (progressIndicator._connectionErrorHandler) {
                            progressIndicator.removeEventListener('connectionerror', progressIndicator._connectionErrorHandler);
                        }
                        if (progressIndicator._autoHideHandler) {
                            progressIndicator.removeEventListener('autohide', progressIndicator._autoHideHandler);
                        }

                        // Define and store event handlers for cleanup
                        progressIndicator._completedHandler = async (event) => {
                            console.log('Analysis completed:', event.detail);

                            // Reset button state
                            btnText.style.display = 'inline';
                            btnLoading.style.display = 'none';
                            reanalyzeBtn.disabled = false;

                            // Fetch updated bias data after a delay to allow users to see completion
                            setTimeout(async () => {
                                await updateBiasAnalysis(articleId);
                            }, 4000); // 4 second delay to allow users to see the completion state
                        };

                        progressIndicator._errorHandler = (event) => {
                            console.error('Analysis error:', event.detail);

                            // Hide progress indicator
                            progressIndicator.style.display = 'none';

                            // Reset button state with error message
                            btnText.textContent = 'Error - Try Again';
                            btnText.style.display = 'inline';
                            btnLoading.style.display = 'none';
                            reanalyzeBtn.disabled = false;

                            // Reset button text after a few seconds
                            setTimeout(() => {
                                btnText.textContent = 'Request Reanalysis';
                            }, 3000);
                        };

                        progressIndicator._connectionErrorHandler = (event) => {
                            console.error('Connection error:', event.detail);

                            // Only reset button if analysis hasn't completed
                            if (reanalyzeBtn.disabled) {
                                // Reset button state
                                btnText.textContent = 'Connection Error - Try Again';
                                btnText.style.display = 'inline';
                                btnLoading.style.display = 'none';
                                reanalyzeBtn.disabled = false;

                                // Reset button text after a few seconds
                                setTimeout(() => {
                                    btnText.textContent = 'Request Reanalysis';
                                }, 3000);
                            } else {
                                console.log('Connection closed after completion - keeping progress visible');
                            }
                        };

                        // Add autohide handler for delayed hiding after completion
                        progressIndicator._autoHideHandler = (event) => {
                            console.log('Auto-hiding progress indicator after completion:', event.detail);
                            progressIndicator.style.display = 'none';
                            progressIndicator.classList.add('progress-hidden');
                        };

                        // Add the event listeners
                        progressIndicator.addEventListener('completed', progressIndicator._completedHandler);
                        progressIndicator.addEventListener('error', progressIndicator._errorHandler);
                        progressIndicator.addEventListener('connectionerror', progressIndicator._connectionErrorHandler);
                        progressIndicator.addEventListener('autohide', progressIndicator._autoHideHandler);
                    }

                    // Connect to SSE for progress updates AFTER event listeners are set up
                    if (progressIndicator && typeof progressIndicator.connect === 'function') {
                        console.log('🔌 Connecting ProgressIndicator to SSE');
                        progressIndicator.connect(articleId);
                    } else {
                        console.warn('ProgressIndicator connect method not available');
                    }

                    // Disable button and show loading state
                    this.disabled = true;
                    btnText.style.display = 'none';
                    btnLoading.style.display = 'inline';
                    
                    try {
                        // Trigger reanalysis
                        const response = await fetch(`/api/llm/reanalyze/${articleId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        // Show progress indicator AFTER event listeners are set up
                        // This prevents race condition with auto-connect="true"
                        progressIndicator.classList.remove('progress-hidden');
                        progressIndicator.style.display = 'block';

                    } catch (error) {
                        console.error('Error requesting reanalysis:', error);
                        
                        // Hide progress indicator
                        progressIndicator.style.display = 'none';
                        
                        // Reset button state with error message
                        btnText.textContent = 'Request Failed - Try Again';
                        btnText.style.display = 'inline';
                        btnLoading.style.display = 'none';
                        reanalyzeBtn.disabled = false;
                        
                        // Reset button text after a few seconds
                        setTimeout(() => {
                            btnText.textContent = 'Request Reanalysis';
                        }, 3000);
                    }
                });
            }
            
            // Function to update bias analysis UI with fresh data
            async function updateBiasAnalysis(articleId) {
                try {
                    const response = await fetch(`/api/articles/${articleId}/bias`);
                    if (!response.ok) {
                        throw new Error(`Failed to fetch bias data: ${response.statusText}`);
                    }
                    
                    const biasData = await response.json();
                    
                    // Update bias score with the composite score
                    // Handle both old format (biasData.composite_score) and new format (biasData.data.composite_score)
                    let compositeScore = null;
                    if (biasData.composite_score !== undefined) {
                        compositeScore = biasData.composite_score;
                    } else if (biasData.data && biasData.data.composite_score !== undefined) {
                        compositeScore = biasData.data.composite_score;
                    }
                    
                    if (biasScoreElement && compositeScore !== null) {
                        biasScoreElement.textContent = `Bias Score: ${compositeScore.toFixed(3)}`;
                    } else if (biasScoreElement) {
                        biasScoreElement.textContent = 'Bias Score: Analysis pending';
                    }
                    
                    console.log('Bias analysis updated with fresh data');
                    
                } catch (error) {
                    console.error('Error updating bias analysis:', error);
                    
                    // Show a subtle notification that data couldn't be updated
                    if (biasScoreElement) {
                        const originalText = biasScoreElement.textContent;
                        biasScoreElement.textContent = 'Analysis complete - refresh page for updated results';
                        biasScoreElement.style.fontStyle = 'italic';

                        setTimeout(() => {
                            biasScoreElement.textContent = originalText;
                            biasScoreElement.style.fontStyle = 'normal';
                        }, 5000);
                    }
                }
            }
        });
    </script>
</body>
</html>
