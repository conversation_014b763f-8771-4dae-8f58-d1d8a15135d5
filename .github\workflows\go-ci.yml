name: Go CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

permissions:
  contents: read
  actions: read
  security-events: write

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      DATABASE_URL: news.db

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.23'

    - name: Install dependencies
      run: go mod tidy

    # The project uses 'averaging everywhere' for LLM score calculation. All tests must pass.

    - name: Validate database
      run: |
        sqlite3 "$DATABASE_URL" '.schema'

    - name: Build test server binary for CI
      run: |
        go build -o test-server-ci ./cmd/server
        chmod +x test-server-ci

    - name: Run Go unit and integration tests (NO_AUTO_ANALYZE=true)
      env:
        NO_AUTO_ANALYZE: "true"
        NO_DOCKER: "true"
        LLM_API_KEY: ${{ secrets.LLM_API_KEY || 'test-key' }}
        LLM_API_KEY_SECONDARY: ${{ secrets.LLM_API_KEY_SECONDARY || '' }}
        LLM_BASE_URL: ${{ secrets.LLM_BASE_URL || '' }}
      run: |
        go test -v -coverprofile=coverage.out ./...

    - name: Set up Node.js for Newman tests
      if: runner.os == 'Linux'
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install Newman for API testing
      if: runner.os == 'Linux'
      run: npm install -g newman

    - name: Run full API/integration test suite (scripts/test.sh all)
      if: runner.os == 'Linux'
      env:
        LLM_API_KEY: ${{ secrets.LLM_API_KEY || 'test-key' }}
        LLM_API_KEY_SECONDARY: ${{ secrets.LLM_API_KEY_SECONDARY || '' }}
        LLM_BASE_URL: ${{ secrets.LLM_BASE_URL || '' }}
      run: |
        chmod +x scripts/test.sh
        retries=0
        until [ $retries -ge 3 ]; do
          ./scripts/test.sh all && break
          retries=$((retries+1))
          echo "Retry $retries..."
          sleep 5
        done

    - name: Upload test-results artifact
      if: runner.os == 'Linux'
      uses: actions/upload-artifact@v4
      with:
        name: test-results
        path: test-results/

    - name: Upload server logs
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: server-logs
        path: test-results/*.log

    - name: Generate coverage summary
      run: |
        go tool cover -func=coverage.out -o coverage-summary.txt

    - name: Generate coverage badge
      uses: tj-actions/coverage-badge-go@v2
      with:
        filename: coverage.out

    - name: Upload coverage artifact
      uses: actions/upload-artifact@v4
      with:
        name: coverage
        path: |
          coverage.out
          coverage-summary.txt
          coverage-badge.svg

    # To enable OpenAI live integration test, uncomment and set these secrets or env vars:
    # env:
    #   LLM_PROVIDER: openai
    #   OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
    #   OPENAI_MODEL: gpt-3.5-turbo
