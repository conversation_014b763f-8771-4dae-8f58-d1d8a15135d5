# Tools Configuration


## Environment

GOBIN: undefined
toolsGopath:
gopath: C:\Users\<USER>\go
GOROOT: C:\Program Files\Go
PATH: C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\usbipd-win\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\PuTTY\;C:\Program Files\Calibre2\;C:\Program Files\Cloudflare\Cloudflare WARP\;C:\Program Files\Go\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\go\bin

## Tools

	go:	C:\Program Files\Go\bin\go.exe: go version go1.24.2 windows/amd64

	gopls:	C:\Users\<USER>\go\bin\gopls.exe	(version: v0.18.1 built with go: go1.24.2)
	gotests:	not installed
	gomodifytags:	not installed
	impl:	not installed
	goplay:	not installed
	dlv:	not installed
	staticcheck:	C:\Users\<USER>\go\bin\staticcheck.exe	(version: v0.6.1 built with go: go1.24.2)

## Go env

Workspace Folder (newbalancer_go): c:\Users\<USER>\Documents\dev\news_filter\newbalancer_go

	set AR=ar
	set CC=gcc
	set CGO_CFLAGS=-O2 -g
	set CGO_CPPFLAGS=
	set CGO_CXXFLAGS=-O2 -g
	set CGO_ENABLED=0
	set CGO_FFLAGS=-O2 -g
	set CGO_LDFLAGS=-O2 -g
	set CXX=g++
	set GCCGO=gccgo
	set GO111MODULE=
	set GOAMD64=v1
	set GOARCH=amd64
	set GOAUTH=netrc
	set GOBIN=
	set GOCACHE=C:\Users\<USER>\AppData\Local\go-build
	set GOCACHEPROG=
	set GODEBUG=
	set GOENV=C:\Users\<USER>\AppData\Roaming\go\env
	set GOEXE=.exe
	set GOEXPERIMENT=
	set GOFIPS140=off
	set GOFLAGS=
	set GOGCCFLAGS=-m64 -fno-caret-diagnostics -Qunused-arguments -Wl,--no-gc-sections -fmessage-length=0 -ffile-prefix-map=C:\Users\<USER>\AppData\Local\Temp\go-build558527079=/tmp/go-build -gno-record-gcc-switches
	set GOHOSTARCH=amd64
	set GOHOSTOS=windows
	set GOINSECURE=
	set GOMOD=c:\Users\<USER>\Documents\dev\news_filter\newbalancer_go\go.mod
	set GOMODCACHE=C:\Users\<USER>\go\pkg\mod
	set GONOPROXY=
	set GONOSUMDB=
	set GOOS=windows
	set GOPATH=C:\Users\<USER>\go
	set GOPRIVATE=
	set GOPROXY=https://proxy.golang.org,direct
	set GOROOT=C:\Program Files\Go
	set GOSUMDB=sum.golang.org
	set GOTELEMETRY=local
	set GOTELEMETRYDIR=C:\Users\<USER>\AppData\Roaming\go\telemetry
	set GOTMPDIR=
	set GOTOOLCHAIN=auto
	set GOTOOLDIR=C:\Program Files\Go\pkg\tool\windows_amd64
	set GOVCS=
	set GOVERSION=go1.24.2
	set GOWORK=
	set PKG_CONFIG=pkg-config
