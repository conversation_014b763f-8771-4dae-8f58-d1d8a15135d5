<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NewsBalancer - Articles</title>

    <!-- Unified CSS System -->
    <link rel="stylesheet" href="/static/css/app-consolidated.css?v=1" />

    <!-- HTMX for dynamic functionality -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Custom NewsBalancer styles -->

</head>
<body>    <header class="navbar">
        <div class="container">
            <a href="/articles" class="navbar-brand">NewsBalancer</a>
            <nav class="navbar-nav" role="navigation" aria-label="Main navigation">
                <a href="/articles"
                   hx-get="/articles"
                   hx-target="body"
                   hx-push-url="true"
                   class="active">Articles</a>
                <a href="/admin">Admin</a>
            </nav>
        </div>
    </header>

    <main class="container">
        <h1>Latest News Articles</h1>          <form class="filter-form"
              role="search" 
              aria-label="Filter articles"
              hx-get="/htmx/articles"
              hx-target="#articles-container"
              hx-trigger="submit, change from:select"
              hx-indicator="#loading-indicator">
            <label for="source-select" class="sr-only">Filter by source:</label>
            <select name="source" id="source-select" aria-label="Source filter">
                <option value="">All Sources</option>
                {{range .Sources}}
                <option value="{{.}}" {{if eq . $.SelectedSource}}selected{{end}}>{{.}}</option>
                {{end}}
            </select>
            
            <label for="bias-select" class="sr-only">Filter by bias:</label>
            <select name="bias" id="bias-select" aria-label="Bias filter">
                <option value="">All Bias Levels</option>
                <option value="left" {{if eq .SelectedBias "left"}}selected{{end}}>Left Leaning</option>
                <option value="center" {{if eq .SelectedBias "center"}}selected{{end}}>Center</option>                <option value="right" {{if eq .SelectedBias "right"}}selected{{end}}>Right Leaning</option>
            </select>
              <label for="search-input" class="sr-only">Search articles:</label>
            <input type="text" name="query" id="search-input" data-testid="search-input" placeholder="Search..." value="{{.SearchQuery}}" aria-label="Search articles">
              <button type="submit">Filter</button>
            
            <!-- HTMX Loading indicator -->
            <div id="loading-indicator" class="htmx-indicator loading-hidden">
                Loading...
            </div>
        </form>
        
                
        <div class="results-summary">
            {{if .Articles}}
            <span>Showing {{len .Articles}} of {{.TotalResults}} articles</span>
            {{if or .SearchQuery .SelectedSource .SelectedBias}}
            <span class="filter-info">
                (filtered{{if .SearchQuery}} for "{{.SearchQuery}}"{{end}}{{if .SelectedSource}} from {{.SelectedSource}}{{end}}{{if .SelectedBias}} with {{.SelectedBias}} bias{{end}})
            </span>
            {{end}}
            {{else}}
            <span>No articles found matching your criteria.</span>
            {{end}}
        </div>
<div class="articles-grid" id="articles-container" data-testid="articles-container">
            {{range .Articles}}
            <div class="article-item" data-testid="article-card-{{.ID}}" data-article-id="{{.ID}}">
                <div class="article-title">
                    <a href="/article/{{.ID}}" data-testid="article-link-{{.ID}}">{{.Title}}</a>
                </div><div class="article-meta">
                    <div>Source: {{.Source}}</div>
                    <div>Published: {{.PubDate.Format "2006-01-02 15:04"}}</div>
                </div>
                <div>
                    {{if eq .Bias "left"}}
                    <span class="bias-indicator bias-left">Left Leaning</span>
                    {{else if eq .Bias "center"}}
                    <span class="bias-indicator bias-center">Center</span>
                    {{else if eq .Bias "right"}}
                    <span class="bias-indicator bias-right">Right Leaning</span>
                    {{end}}
                </div>
            </div>            {{else}}
            <p data-testid="no-results">No articles found. Try adjusting your filters.</p>
            {{end}}
        </div>
        
        <!-- Load more button for dynamic loading -->
        <div id="load-more-container" data-testid="load-more-container" class="load-more-section text-center my-4">
            {{if .HasMore}}
            <button id="load-more-btn"
                    data-testid="load-more-articles"
                    class="btn btn-primary"
                    hx-get="/htmx/articles/load-more"
                    hx-target="#articles-container"
                    hx-swap="beforeend"
                    hx-vals='{"page": "{{.NextPage}}"}'
                    hx-indicator="#loading-indicator">
                Load More Articles
            </button>
            {{else}}
            <button data-testid="load-more-articles"
                    class="btn btn-primary"
                    hx-get="/htmx/articles/load-more"
                    hx-target="#articles-container"
                    hx-swap="beforeend"
                    hx-vals='{"page": "2"}'
                    hx-indicator="#loading-indicator">
                Load More Articles
            </button>
            {{end}}
        </div>
        
        <div class="pagination">
            {{if gt .CurrentPage 1}}
            <a href="?page={{.PrevPage}}{{if .SearchQuery}}&query={{.SearchQuery}}{{end}}{{if .SelectedSource}}&source={{.SelectedSource}}{{end}}{{if .SelectedBias}}&bias={{.SelectedBias}}{{end}}">&laquo; Previous</a>
            {{end}}
            
            {{range .Pages}}
            <a href="?page={{.}}{{if $.SearchQuery}}&query={{$.SearchQuery}}{{end}}{{if $.SelectedSource}}&source={{$.SelectedSource}}{{end}}{{if $.SelectedBias}}&bias={{$.SelectedBias}}{{end}}" {{if eq . $.CurrentPage}}class="active"{{end}}>{{.}}</a>
            {{end}}
            
            {{if lt .CurrentPage .TotalPages}}
            <a href="?page={{.NextPage}}{{if .SearchQuery}}&query={{.SearchQuery}}{{end}}{{if .SelectedSource}}&source={{.SelectedSource}}{{end}}{{if .SelectedBias}}&bias={{.SelectedBias}}{{end}}">Next &raquo;</a>
            {{end}}        </div>
    </main>

<script>
// Enhanced filtering and search functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('.filter-form');
    const searchInput = document.querySelector('input[name="query"]');
    const sourceSelect = document.querySelector('select[name="source"]');
    const biasSelect = document.querySelector('select[name="bias"]');
    let searchTimeout;
    
    // Auto-submit on filter changes
    if (sourceSelect) {
        sourceSelect.addEventListener('change', function() {
            filterForm.submit();
        });
    }
    
    if (biasSelect) {
        biasSelect.addEventListener('change', function() {
            filterForm.submit();
        });
    }
    
    // Debounced search
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                filterForm.submit();
            }, 500); // 500ms delay
        });
    }
    
    // Clear filters functionality
    const clearButton = document.getElementById('clear-filters');
    if (clearButton) {
        clearButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Reset all form fields
            if (sourceSelect) sourceSelect.selectedIndex = 0;
            if (biasSelect) biasSelect.selectedIndex = 0;
            if (searchInput) searchInput.value = '';
            
            // Submit the cleared form
            filterForm.submit();
        });
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+/ or Cmd+/ to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === '/') {
            e.preventDefault();
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
        
        // Escape to clear search
        if (e.key === 'Escape' && document.activeElement === searchInput) {
            searchInput.value = '';
            filterForm.submit();
        }
    });
});
</script>
</body>
</html>

