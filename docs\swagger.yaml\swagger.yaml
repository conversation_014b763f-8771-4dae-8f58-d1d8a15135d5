definitions:
  github_com_alexandru-sa<PERSON><PERSON>_BalancedNewsGo_internal_models.ProgressState:
    description: Progress state for long-running operations
    properties:
      error:
        description: Error message if failed
        type: string
      error_details:
        description: Structured error details (JSON string)
        type: string
      final_score:
        description: Final score if completed
        example: 0.25
        type: number
      last_updated:
        description: Timestamp
        example: 1609459200
        type: integer
      message:
        description: User-friendly message
        example: Processing article
        type: string
      percent:
        description: Progress percentage
        example: 75
        type: integer
      status:
        description: Overall status
        example: InProgress
        type: string
      step:
        description: Current detailed step
        example: Scoring
        type: string
    type: object
  internal_api.ArticleResponse:
    properties:
      article_id:
        type: integer
      composite_score:
        type: number
      confidence:
        type: number
      content:
        type: string
      published_at:
        type: string
      score_source:
        type: string
      source:
        type: string
      title:
        type: string
    type: object
  internal_api.CreateArticleRequest:
    description: Request body for creating a new article
    properties:
      content:
        description: Article content
        example: Article content...
        type: string
      pub_date:
        description: Publication date in RFC3339 format
        example: "2023-01-01T12:00:00Z"
        type: string
      source:
        description: News source name
        example: CNN
        type: string
      title:
        description: Article title
        example: Breaking News
        type: string
      url:
        description: Article URL
        example: https://example.com/article
        type: string
    required:
    - content
    - pub_date
    - source
    - title
    - url
    type: object
  internal_api.CreateArticleResponse:
    description: Response from creating a new article
    properties:
      article_id:
        description: ID of the created article
        example: 42
        type: integer
      status:
        description: Status of the operation
        example: created
        type: string
    type: object
  internal_api.ErrorDetail:
    description: Detailed error information
    properties:
      code:
        description: Error code
        example: validation_error
        type: string
      message:
        description: Human-readable error message
        example: Invalid input parameters
        type: string
    type: object
  internal_api.ErrorResponse:
    description: Standard API error response
    properties:
      error:
        allOf:
        - $ref: '#/definitions/internal_api.ErrorDetail'
        description: Error details
      success:
        description: Always false for errors
        example: false
        type: boolean
    type: object
  internal_api.FeedbackRequest:
    description: Request body for submitting user feedback
    properties:
      article_id:
        description: Article ID
        type: integer
      category:
        description: 'Feedback category: agree, disagree, unclear, other'
        example: agree
        type: string
      ensemble_output_id:
        description: ID of specific ensemble output
        type: integer
      feedback_text:
        description: Feedback content
        type: string
      source:
        description: Source of the feedback
        example: web
        type: string
      user_id:
        description: User ID
        type: string
    required:
    - article_id
    - feedback_text
    - user_id
    type: object
  internal_api.IndividualScoreResult:
    description: Individual model scoring result
    properties:
      confidence:
        description: Model confidence
        example: 0.8
        type: number
      created_at:
        description: When the score was generated
        type: string
      explanation:
        description: Explanation for the score
        example: Reasoning
        type: string
      model:
        description: Model name
        example: claude-3
        type: string
      score:
        description: Bias score
        example: 0.3
        type: number
    type: object
  internal_api.ManualScoreRequest:
    description: Request body for manually setting an article's bias score
    properties:
      score:
        description: Score value between -1.0 and 1.0
        example: 0.5
        type: number
    required:
    - score
    type: object
  internal_api.ScoreResponse:
    description: Political bias score analysis result
    properties:
      composite_score:
        description: Overall bias score
        example: 0.25
        type: number
      results:
        description: Individual model scores
        items:
          $ref: '#/definitions/internal_api.IndividualScoreResult'
        type: array
      status:
        description: Status message if applicable
        example: scoring_unavailable
        type: string
    type: object
  internal_api.StandardResponse:
    description: Standard API success response
    properties:
      data:
        description: Response data payload
      success:
        description: Always true for success
        example: true
        type: boolean
    type: object
info:
  contact: {}
paths:
  /api/articles:
    get:
      consumes:
      - application/json
      description: Fetches a list of articles with optional filtering by source, leaning,
        and pagination
      operationId: getArticlesList
      parameters:
      - description: Filter by news source
        in: query
        name: source
        type: string
      - description: Filter by political leaning (left/center/right)
        in: query
        name: leaning
        type: string
      - default: 0
        description: Pagination offset
        in: query
        minimum: 0
        name: offset
        type: integer
      - default: 20
        description: Number of items per page
        in: query
        maximum: 100
        minimum: 1
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of articles
          schema:
            allOf:
            - $ref: '#/definitions/internal_api.StandardResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/internal_api.ArticleResponse'
                  type: array
              type: object
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
      summary: Get articles
      tags:
      - Articles
    post:
      consumes:
      - application/json
      description: Creates a new article with the provided information
      operationId: createArticle
      parameters:
      - description: Article information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/internal_api.CreateArticleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Article created successfully
          schema:
            allOf:
            - $ref: '#/definitions/internal_api.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/internal_api.CreateArticleResponse'
              type: object
        "400":
          description: Invalid request data
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "409":
          description: Article URL already exists
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
      summary: Create article
      tags:
      - Articles
  /api/articles/{id}:
    get:
      consumes:
      - application/json
      description: Fetches a specific article by its ID with scores and metadata
      operationId: getArticleById
      parameters:
      - description: Article ID
        in: path
        minimum: 1
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Success with article details
          schema:
            $ref: '#/definitions/internal_api.StandardResponse'
        "400":
          description: Invalid article ID
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "404":
          description: Article not found
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
      summary: Get article by ID
      tags:
      - Articles
  /api/articles/{id}/bias:
    get:
      consumes:
      - application/json
      description: Retrieves the political bias score and individual model results
        for an article
      operationId: getArticleBias
      parameters:
      - description: Article ID
        in: path
        minimum: 1
        name: id
        required: true
        type: integer
      - default: -1
        description: Minimum score filter
        in: query
        maximum: 1
        minimum: -1
        name: min_score
        type: number
      - default: 1
        description: Maximum score filter
        in: query
        maximum: 1
        minimum: -1
        name: max_score
        type: number
      - default: desc
        description: Sort order (asc or desc)
        enum:
        - asc
        - desc
        in: query
        name: sort
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            allOf:
            - $ref: '#/definitions/internal_api.StandardResponse'
            - properties:
                data:
                  $ref: '#/definitions/internal_api.ScoreResponse'
              type: object
        "400":
          description: Invalid parameters
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "404":
          description: Article not found
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
      summary: Get article bias analysis
      tags:
      - Analysis
  /api/articles/{id}/ensemble:
    get:
      description: Retrieves individual model results and aggregation for an article's
        ensemble score
      operationId: getArticleEnsemble
      parameters:
      - description: Article ID
        in: path
        minimum: 1
        name: id
        required: true
        type: integer
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/internal_api.StandardResponse'
        "404":
          description: Ensemble data not found
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
      summary: Get ensemble scoring details
      tags:
      - Analysis
  /api/feedback:
    post:
      consumes:
      - application/json
      description: Submit user feedback on an article's political bias analysis
      operationId: submitFeedback
      parameters:
      - description: Feedback information
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/internal_api.FeedbackRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Feedback received
          schema:
            $ref: '#/definitions/internal_api.StandardResponse'
        "400":
          description: Invalid request data
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
      summary: Submit user feedback
      tags:
      - Feedback
  /api/feeds/healthz:
    get:
      consumes:
      - application/json
      description: Returns the health status of all configured RSS feeds
      operationId: getFeedsHealth
      produces:
      - application/json
      responses:
        "200":
          description: Feed health status mapping feed names to boolean status
          schema:
            additionalProperties:
              type: boolean
            type: object
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
      summary: Get RSS feed health status
      tags:
      - Feeds
  /api/llm/reanalyze/{id}:
    post:
      consumes:
      - application/json
      description: Trigger a new LLM analysis for a specific article and update its
        scores.
      operationId: reanalyzeArticle
      parameters:
      - description: Article ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "202":
          description: Reanalysis started
          schema:
            $ref: '#/definitions/internal_api.StandardResponse'
        "400":
          description: Invalid article ID
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "401":
          description: LLM authentication failed
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "402":
          description: LLM payment required or credits exhausted
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "404":
          description: Article not found
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "429":
          description: LLM rate limit exceeded
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
        "503":
          description: LLM service unavailable or streaming error
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
      summary: Reanalyze article
      tags:
      - LLM
  /api/llm/score-progress/{id}:
    get:
      operationId: getScoreProgress
      parameters:
      - description: Article ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - text/event-stream
      responses:
        "200":
          description: SSE stream of progress updates
          schema:
            $ref: '#/definitions/github_com_alexandru-savinov_BalancedNewsGo_internal_models.ProgressState'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/internal_api.StandardResponse'
      summary: Stream LLM scoring progress
  /api/manual-score/{id}:
    post:
      description: Updates an article's bias score manually
      operationId: addManualScore
      parameters:
      - description: Article ID
        in: path
        minimum: 1
        name: id
        required: true
        type: integer
      - description: Score value between -1.0 and 1.0
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/internal_api.ManualScoreRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/internal_api.StandardResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
      summary: Manually set article score
      tags:
      - Analysis
  /api/refresh:
    post:
      consumes:
      - application/json
      description: Initiates a manual RSS feed refresh job to fetch new articles from
        configured RSS sources
      operationId: triggerRssRefresh
      produces:
      - application/json
      responses:
        "200":
          description: Refresh started successfully
          schema:
            allOf:
            - $ref: '#/definitions/internal_api.StandardResponse'
            - properties:
                data:
                  additionalProperties:
                    type: string
                  type: object
              type: object
        "500":
          description: Server error
          schema:
            $ref: '#/definitions/internal_api.ErrorResponse'
      summary: Trigger RSS feed refresh
      tags:
      - Feeds
swagger: "2.0"
