run:
  # Skip test files for cleaner linting (test files often have acceptable loose error handling)
  skip-tests: true
  timeout: 5m

issues:
  # Don't exclude issues in vendor or generated files
  exclude-use-default: false
  exclude-rules:
    # Allow unchecked errors in test files for common test operations
    - path: "_test\\.go$"
      linters:
        - errcheck

linters-settings:
  gocritic:
    disabled-checkers:
      - exitAfterDefer
  revive:
    ignore-generated-header: true
  errcheck:
    check-type-assertions: true
    # Don't check when we explicitly ignore errors with _
    check-blank: false
    # Ignore common cleanup functions where errors are often ignored
    exclude-functions:
      - fmt.Print
      - fmt.Printf
      - fmt.Println
      - (net/http.ResponseWriter).Write
      - (*net/http.Response).Body.Close
  govet:
    # Enable checks for common concurrency issues
    enable:
      - atomic
      - bools
      - buildtag
      - copylocks
      - httpresponse
      - loopclosure
      - lostcancel
      - nilfunc
      - printf
      - shift
      - stdmethods
      - structtag
      - tests
      - unmarshal
      - unreachable
      - unsafeptr
      - unusedresult
  staticcheck:
    # Enable all staticcheck rules including concurrency checks
    checks: ["all"]
  gosec:
    # Enable security checks that can catch concurrency issues
    includes:
      - G104  # Audit errors not checked
      - G204  # Audit use of command execution
      - G304  # File path provided as taint input

linters:
  enable:
    # Core linters
    - govet
    - revive
    - gosimple
    - misspell
    # Concurrency and race condition detection
    - staticcheck  # Excellent for detecting concurrency issues
    - errcheck     # Important for checking error handling in concurrent code
    - ineffassign  # Can catch issues with concurrent assignments
    - gosec        # Security issues including some concurrency problems
    # Additional useful linters
    - typecheck
    - unused
  disable:
    - depguard
    - forbidigo
    - gocyclo
    - funlen
    - gocritic
    - goconst
    - unparam
    - gofmt
    - goimports
