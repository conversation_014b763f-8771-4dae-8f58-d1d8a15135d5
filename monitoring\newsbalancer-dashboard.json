{"dashboard": {"id": null, "title": "NewsBalancer - Main Dashboard", "tags": ["newsbalancer", "api", "llm", "monitoring"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "API Request Rate", "type": "stat", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "Requests/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "API Response Time", "type": "stat", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 0.5}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Error Rate", "type": "stat", "targets": [{"expr": "rate(http_requests_total{status=~\"4..|5..\"}[5m]) / rate(http_requests_total[5m]) * 100", "legendFormat": "Error %"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Active Articles", "type": "stat", "targets": [{"expr": "newsbalancer_articles_total", "legendFormat": "Total Articles"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "LLM Analysis Queue", "type": "graph", "targets": [{"expr": "newsbalancer_llm_queue_size", "legendFormat": "<PERSON><PERSON> Size"}, {"expr": "rate(newsbalancer_llm_analyses_total[5m])", "legendFormat": "Analyses/sec"}], "yAxes": [{"label": "Count", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "LLM Response Times", "type": "graph", "targets": [{"expr": "histogram_quantile(0.50, rate(newsbalancer_llm_request_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}, {"expr": "histogram_quantile(0.95, rate(newsbalancer_llm_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.99, rate(newsbalancer_llm_request_duration_seconds_bucket[5m]))", "legendFormat": "99th percentile"}], "yAxes": [{"label": "Seconds", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "Database Connections", "type": "graph", "targets": [{"expr": "newsbalancer_db_connections_active", "legendFormat": "Active Connections"}, {"expr": "newsbalancer_db_connections_idle", "legendFormat": "Idle Connections"}], "yAxes": [{"label": "Connections", "min": 0}], "gridPos": {"h": 8, "w": 8, "x": 0, "y": 16}}, {"id": 8, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "process_resident_memory_bytes", "legendFormat": "RSS Memory"}, {"expr": "go_memstats_heap_inuse_bytes", "legendFormat": "Heap Memory"}], "yAxes": [{"label": "Bytes", "min": 0}], "gridPos": {"h": 8, "w": 8, "x": 8, "y": 16}}, {"id": 9, "title": "CPU Usage", "type": "graph", "targets": [{"expr": "rate(process_cpu_seconds_total[5m]) * 100", "legendFormat": "CPU %"}], "yAxes": [{"label": "Percent", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 8, "x": 16, "y": 16}}]}}