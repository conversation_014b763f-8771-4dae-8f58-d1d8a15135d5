/* Base Styles and Reset */
/* CSS reset/normalize and global typography */

/* Modern CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

/* Remove list styles on ul, ol elements with a list role */
ul[role="list"],
ol[role="list"] {
  list-style: none;
}

/* Set core root defaults */
html:focus-within {
  scroll-behavior: smooth;
}

html {
  font-size: 16px; /* Base font size for rem calculations */
}

/* Set core body defaults */
body {
  min-height: 100vh;
  text-rendering: optimizeSpeed;
  font-family: var(--font-primary, "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont, "Helvetica Neue", Arial, sans-serif);
  font-size: var(--font-size-base, 1rem);
  line-height: var(--line-height-base, 1.6);
  color: var(--color-text, #333);
  background-color: var(--color-bg, #fff);
  font-weight: var(--font-weight-normal, 400);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* A elements that don't have a class get default styles */
a:not([class]) {
  text-decoration-skip-ink: auto;
}

/* Make images easier to work with */
img,
picture {
  max-width: 100%;
  display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
  font: inherit;
}

/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
    scroll-behavior: auto;
  }

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Typography Elements */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold, 700);
  line-height: var(--line-height-tight, 1.25);
  color: var(--color-text, #333);
  margin-top: 0;
  margin-bottom: var(--space-4, 1rem);
}

/* Heading Sizes - h1 specifically set to 2rem per acceptance criteria */
h1 {
  font-size: 2rem;
  margin-bottom: var(--space-6, 1.5rem);
}

h2 {
  font-size: var(--font-size-3xl, 1.875rem);
  margin-bottom: var(--space-5, 1.25rem);
}

h3 {
  font-size: var(--font-size-2xl, 1.5rem);
  margin-bottom: var(--space-4, 1rem);
}

h4 {
  font-size: var(--font-size-xl, 1.25rem);
  margin-bottom: var(--space-3, 0.75rem);
}

h5 {
  font-size: var(--font-size-lg, 1.125rem);
  margin-bottom: var(--space-3, 0.75rem);
}

h6 {
  font-size: var(--font-size-base, 1rem);
  margin-bottom: var(--space-2, 0.5rem);
}

p {
  margin-bottom: 1rem;
}

/* Link Styles */
a {
  color: var(--color-primary, #007bff);
  text-decoration: none;
  transition: color 0.15s ease-in-out;
}

a:hover {
  color: var(--color-primary, #007bff);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--color-primary, #007bff);
  outline-offset: 2px;
}

a:visited {
  color: var(--color-primary, #007bff);
}

/* Lists */
ul, ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
}
