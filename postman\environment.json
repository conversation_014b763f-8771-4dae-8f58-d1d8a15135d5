{"id": "0f45868d-7f63-4c8a-ae39-1131feb93ce8", "name": "NewsBalancer Local", "values": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "default", "enabled": true}, {"key": "articleId", "value": "", "type": "default", "enabled": true}, {"key": "scoringArticleId", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-05-03T12:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}