# SonarCloud Configuration for BalancedNewsGo
sonar.projectKey=alexandru-savinov_BalancedNewsGo
sonar.organization=alexandru-savinov

# This is the name and version displayed in the SonarCloud UI.
sonar.projectName=BalancedNewsGo
sonar.projectVersion=1.0

# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
sonar.sources=.

# Encoding of the source code. Default is default system encoding
sonar.sourceEncoding=UTF-8

# Exclusions for analysis
sonar.exclusions=**/node_modules/**,**/bin/**,**/coverage/**,**/test-results/**,**/vendor/**,**/*.pb.go,**/docs/**,**/scripts/**,**/monitoring/**,**/postman/**,**/sqlite-tools/**,**/backup/**,**/static/js/**/*.test.js,**/jest.config.js,**/test-*.js,**/test-*.ps1,**/*.md

# Test Configuration
sonar.tests=.
sonar.test.inclusions=**/*_test.go,**/tests/**/*.go,**/tests/**/*.ts,**/tests/**/*.js
sonar.test.exclusions=**/node_modules/**,**/bin/**,**/vendor/**

# Go Language Configuration
sonar.go.coverage.reportPaths=coverage.out

# Coverage Exclusions - Files that should not be included in coverage calculations
sonar.coverage.exclusions=**/static/js/**,**/tests/**,**/*_test.go,**/cmd/**,**/tools/**,**/internal/testing/**,**/docs/**,**/*.md,**/*.js,**/*.ts

# Security Configuration - Focus on security issues
sonar.security.hotspots.inheritFromParent=true
