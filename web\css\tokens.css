/* Design Tokens - CSS Custom Properties */
/* Global design tokens for colors, typography, spacing, etc. */

:root {
  /* Brand Colors */
  --color-primary: #007bff;        /* Primary blue (from admin buttons) */
  --color-secondary: #6c757d;      /* Secondary gray */
  --color-success: #28a745;        /* Success green */
  --color-warning: #ffc107;        /* Warning yellow */
  --color-danger: #dc3545;         /* Danger red */
  --color-info: #17a2b8;          /* Info cyan */

  /* Neutral Colors */
  --color-white: #fff;
  --color-light: #f8f9fa;          /* Light background */
  --color-gray-100: #f8f9fa;
  --color-gray-200: #e9ecef;
  --color-gray-300: #dee2e6;
  --color-gray-400: #ced4da;
  --color-gray-500: #adb5bd;
  --color-gray-600: #6c757d;
  --color-gray-700: #495057;
  --color-gray-800: #343a40;
  --color-gray-900: #212529;
  --color-dark: #343a40;
  --color-black: #000;

  /* Text Colors */
  --color-text: #333;              /* Primary text color */
  --color-text-muted: #6c757d;     /* Muted text color */
  --color-text-light: #fff;        /* Light text for dark backgrounds */

  /* Background Colors */
  --color-bg: #fff;                /* Primary background */
  --color-bg-light: #f8f9fa;       /* Light background variant */
  --color-bg-dark: #343a40;        /* Dark background */

  /* Bias Indicator Colors */
  --color-bias-left: #0d6efd;      /* Blue for left bias */
  --color-bias-center: #6c757d;    /* Gray for center bias */
  --color-bias-right: #dc3545;     /* Red for right bias */
  --color-bias-unknown: #ffc107;   /* Yellow for unknown bias */

  /* Typography */
  --font-primary: "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont, "Helvetica Neue", Arial, sans-serif;
  --font-secondary: Georgia, "Times New Roman", Times, serif;
  --font-monospace: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;         /* 12px */
  --font-size-sm: 0.875rem;        /* 14px */
  --font-size-base: 1rem;          /* 16px */
  --font-size-lg: 1.125rem;        /* 18px */
  --font-size-xl: 1.25rem;         /* 20px */
  --font-size-2xl: 1.5rem;         /* 24px */
  --font-size-3xl: 1.875rem;       /* 30px */
  --font-size-4xl: 2.25rem;        /* 36px */
  --font-size-5xl: 3rem;           /* 48px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-base: 1.6;
  --line-height-relaxed: 1.75;
  --line-height-loose: 2;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Spacing Scale */
  --space-0: 0;
  --space-px: 1px;
  --space-0-5: 0.125rem;        /* 2px */
  --space-1: 0.25rem;           /* 4px */
  --space-1-5: 0.375rem;        /* 6px */
  --space-2: 0.5rem;            /* 8px */
  --space-2-5: 0.625rem;        /* 10px */
  --space-3: 0.75rem;           /* 12px */
  --space-3-5: 0.875rem;        /* 14px */
  --space-4: 1rem;              /* 16px */
  --space-5: 1.25rem;           /* 20px */
  --space-6: 1.5rem;            /* 24px */
  --space-7: 1.75rem;           /* 28px */
  --space-8: 2rem;              /* 32px */
  --space-9: 2.25rem;           /* 36px */
  --space-10: 2.5rem;           /* 40px */
  --space-11: 2.75rem;          /* 44px */
  --space-12: 3rem;             /* 48px */
  --space-14: 3.5rem;           /* 56px */
  --space-16: 4rem;             /* 64px */
  --space-20: 5rem;             /* 80px */
  --space-24: 6rem;             /* 96px */
  --space-28: 7rem;             /* 112px */
  --space-32: 8rem;             /* 128px */

  /* Semantic Spacing */
  --space-xs: var(--space-1);   /* 4px */
  --space-sm: var(--space-2);   /* 8px */
  --space-md: var(--space-4);   /* 16px */
  --space-lg: var(--space-6);   /* 24px */
  --space-xl: var(--space-8);   /* 32px */
  --space-2xl: var(--space-12); /* 48px */
  --space-3xl: var(--space-16); /* 64px */

  /* Border Radius */
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;    /* 2px */
  --border-radius: 0.25rem;        /* 4px */
  --border-radius-md: 0.375rem;    /* 6px */
  --border-radius-lg: 0.5rem;      /* 8px */
  --border-radius-xl: 0.75rem;     /* 12px */
  --border-radius-2xl: 1rem;       /* 16px */
  --border-radius-3xl: 1.5rem;     /* 24px */
  --border-radius-full: 9999px;    /* Fully rounded */

  /* Border Widths */
  --border-width-0: 0;
  --border-width: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-width-8: 8px;

  /* Container Widths */
  --container-max-width: 1200px;
  --container-sm: 576px;
  --container-md: 768px;
  --container-lg: 992px;
  --container-xl: 1200px;
}
