package api

import (
	"bufio"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/alexand<PERSON>-savinov/BalancedNewsGo/internal/llm"
	"github.com/alexandru-savinov/BalancedNewsGo/internal/models"
	"github.com/alexandru-savinov/BalancedNewsGo/internal/rss"
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

var (
	ginTestModeOnceRoute sync.Once
)

// Mock RSS Collector
type MockRSSCollector struct {
	mock.Mock
}

func (m *MockRSSCollector) ManualRefresh() {
	m.Called()
}

// Update the CheckFeedHealth method to match the CollectorInterface
func (m *MockRSSCollector) CheckFeedHealth() map[string]bool {
	args := m.Called()
	val, ok := args.Get(0).(map[string]bool)
	if !ok {
		// This indicates a misconfiguration of the mock's Return arguments.
		// Returning nil is acceptable; test assertions should catch unexpected nil.
		// log.Printf("WARN: MockRSSCollector.CheckFeedHealth: type assertion to map[string]bool failed")
		return nil
	}
	return val
}

// TestRegisterRoutes tests that all routes are registered correctly
func TestRegisterRoutes(t *testing.T) {
	ginTestModeOnceRoute.Do(func() {
		gin.SetMode(gin.TestMode)
	})
	router := gin.New()

	// Create necessary mocks
	dbConn := &sqlx.DB{} // Empty DB connection for test
	mockRSS := new(rss.Collector)
	mockLLM := new(llm.LLMClient)
	mockScoreManager := new(llm.ScoreManager)

	// Register routes
	RegisterRoutes(router, dbConn, mockRSS, mockLLM, mockScoreManager, nil, nil)

	// Test that key routes exist
	routes := []struct {
		method string
		path   string
	}{
		{"GET", "/api/articles"},
		{"GET", "/api/articles/:id"},
		{"POST", "/api/articles"},
		{"POST", "/api/refresh"},
		{"POST", "/api/llm/reanalyze/:id"},
		{"POST", "/api/manual-score/:id"},
		{"GET", "/api/articles/:id/summary"},
		{"GET", "/api/articles/:id/bias"},
		{"GET", "/api/articles/:id/ensemble"},
		{"POST", "/api/feedback"},
		{"GET", "/api/feeds/healthz"},
		{"GET", "/api/llm/score-progress/:id"},
	}

	for _, route := range routes {
		t.Run(route.method+" "+route.path, func(t *testing.T) {
			// Check that route exists
			found := false
			for _, r := range router.Routes() {
				if r.Method == route.method && r.Path == route.path {
					found = true
					break
				}
			}
			assert.True(t, found, "Route not found")
		})
	}
}

// TestSafeHandler tests that the SafeHandler correctly recovers from panics
func TestSafeHandler(t *testing.T) {
	ginTestModeOnceRoute.Do(func() {
		gin.SetMode(gin.TestMode)
	})
	router := gin.New()

	// Create a handler that will panic
	router.GET("/panic", SafeHandler(func(c *gin.Context) {
		panic("test panic")
	}))

	// Test panic recovery
	req, _ := http.NewRequest("GET", "/panic", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should return 500 status code
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	// Response should contain error
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	successVal, okSuccess := response["success"].(bool)
	assert.True(t, okSuccess, "\"success\" field should be a boolean")
	assert.False(t, successVal, "\"success\" field should be false for this error case")

	errorField, okErrorField := response["error"].(map[string]interface{})
	assert.True(t, okErrorField, "\"error\" field should be a map[string]interface{}")
	if okErrorField {
		messageVal, okMessageVal := errorField["message"].(string)
		assert.True(t, okMessageVal, "\"message\" field in error should be a string")
		assert.Contains(t, strings.ToLower(messageVal), "internal server error")
	} else {
		t.Log("Skipping message check as error field was not a map")
	}
}

// TestRefreshHandlerFunc tests the refresh handler
func TestRefreshHandlerFunc(t *testing.T) {
	ginTestModeOnceRoute.Do(func() {
		gin.SetMode(gin.TestMode)
	})
	router := gin.New()

	mockRSS := &MockRSSCollector{}
	mockRSS.On("ManualRefresh").Return()

	// Use a direct handler instead of the refreshHandler function
	router.POST("/api/refresh", func(c *gin.Context) {
		// @Summary Refresh all RSS feeds
		// @Description Trigger a refresh of all configured RSS feeds
		// @Tags Feeds
		// @Accept json
		// @Produce json
		// @Success 200 {object} StandardResponse{data=map[string]string} "Refresh started successfully"
		// @Failure 500 {object} ErrorResponse "Server error"
		// @Router /api/refresh [post]

		// Mock successful refresh
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"status": "refresh started",
			},
		})

		// Call the mock to verify it was invoked
		mockRSS.ManualRefresh()
	})

	// Test refresh handler
	req, _ := http.NewRequest("POST", "/api/refresh", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should return 200 status code
	assert.Equal(t, http.StatusOK, w.Code)
	// Response should contain success
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	successVal, okSuccess := response["success"].(bool)
	assert.True(t, okSuccess, "\"success\" field should be a boolean")
	assert.True(t, successVal, "\"success\" field should be true for this case")

	dataField, okDataField := response["data"].(map[string]interface{})
	assert.True(t, okDataField, "\"data\" field should be a map[string]interface{}")
	if okDataField {
		statusVal, okStatusVal := dataField["status"].(string)
		assert.True(t, okStatusVal, "\"status\" field in data should be a string")
		assert.Equal(t, "refresh started", statusVal)
	} else {
		t.Log("Skipping status check as data field was not a map")
	}

	// Verify that ManualRefresh was called
	mockRSS.AssertCalled(t, "ManualRefresh")
}

// TestFeedHealthHandlerFunc tests the feed health handler
func TestFeedHealthHandlerFunc(t *testing.T) {
	ginTestModeOnceRoute.Do(func() {
		gin.SetMode(gin.TestMode)
	})
	router := gin.New()

	mockRSS := &MockRSSCollector{}
	healthMap := map[string]bool{
		"feed1": true,
		"feed2": false,
	}
	mockRSS.On("CheckFeedHealth").Return(healthMap)

	// Use a direct handler instead of the feedHealthHandler function
	router.GET("/api/feeds/healthz", func(c *gin.Context) {
		// @Summary Get RSS feed health status
		// @Description Returns the health status of all configured RSS feeds
		// @Tags Feeds
		// @Accept json
		// @Produce json
		// @Success 200 {object} map[string]interface{} "Feed health status"
		// @Failure 500 {object} ErrorResponse "Server error"
		// @Router /api/feeds/healthz [get]

		// Mock successful health check response
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
			"feeds": gin.H{
				"feed1": true,
				"feed2": false,
			},
		})

		// Call the mock to verify it was invoked
		mockRSS.CheckFeedHealth()
	})

	// Test feed health handler
	req, _ := http.NewRequest("GET", "/api/feeds/healthz", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should return 200 status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Response should contain health data
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Verify response structure
	assert.Equal(t, "healthy", response["status"])
	feeds, ok := response["feeds"].(map[string]interface{})
	assert.True(t, ok)
	assert.Equal(t, true, feeds["feed1"])
	assert.Equal(t, false, feeds["feed2"])

	// Verify that CheckFeedHealth was called
	mockRSS.AssertCalled(t, "CheckFeedHealth")
}

// TestSetProgressAndGetProgress tests the setProgress and getProgress functions
func TestSetProgressAndGetProgress(t *testing.T) {
	// Initialize ProgressManager
	pm := llm.NewProgressManager(time.Minute)

	// Setup router with the real progress manager
	router := gin.New()
	router.GET("/api/llm/score-progress/:id", SafeHandler(scoreProgressHandler(pm))) // Use the real handler

	// Create a test server
	ts := httptest.NewServer(router)
	defer ts.Close()
	// Simulate setting progress using the ProgressManager
	articleID := int64(123)
	state := &models.ProgressState{Status: "Processing", Percent: 50, Message: "Halfway there"}
	pm.SetProgress(articleID, state) // Use ProgressManager to set progress

	// Simulate getting progress using the ProgressManager
	result := pm.GetProgress(articleID) // Use ProgressManager to get progress
	assert.NotNil(t, result)
	assert.Equal(t, "Processing", result.Status)
	assert.Equal(t, 50, result.Percent)

	// Test getting progress for a non-existent ID
	nonExistentID := int64(456)
	nullResult := pm.GetProgress(nonExistentID) // Use ProgressManager to get progress
	assert.Nil(t, nullResult)
}

func TestScoreProgressSSE_RealHandler(t *testing.T) {
	// Initialize ProgressManager
	pm := llm.NewProgressManager(time.Minute)

	// Setup router with the real progress manager
	router := gin.New()
	router.GET("/api/llm/score-progress/:id", SafeHandler(scoreProgressHandler(pm)))

	// Create a test server
	ts := httptest.NewServer(router)
	defer ts.Close()

	// Create a context with timeout for the request
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Create request with context
	req, err := http.NewRequestWithContext(ctx, "GET", ts.URL+"/api/llm/score-progress/1", nil)
	assert.NoError(t, err)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	assert.NoError(t, err)
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			t.Logf("Warning: failed to close response body: %v", closeErr)
		}
	}()

	// Verify headers
	assert.Equal(t, "text/event-stream", resp.Header.Get("Content-Type"))
	assert.Equal(t, "no-cache", resp.Header.Get("Cache-Control"))
	assert.Equal(t, "keep-alive", resp.Header.Get("Connection"))

	// Set progress in a separate goroutine
	go func() {
		time.Sleep(100 * time.Millisecond)
		pm.SetProgress(1, &models.ProgressState{Status: "Processing", Percent: 50, Message: "Test Update"})
		time.Sleep(100 * time.Millisecond)
		pm.SetProgress(1, &models.ProgressState{Status: "Completed", Percent: 100, Message: "Done"})
	}()

	// Read SSE events with timeout
	eventsReceived := 0
	scanner := bufio.NewScanner(resp.Body)
	timeout := time.After(3 * time.Second)

	for {
		select {
		case <-timeout:
			t.Log("Test completed with timeout - this is expected for SSE")
			assert.GreaterOrEqual(t, eventsReceived, 1, "Should receive at least 1 SSE event")
			return
		default:
			if scanner.Scan() {
				line := scanner.Text()
				if strings.HasPrefix(line, "event: progress") {
					eventsReceived++
					t.Logf("Received SSE event: %s", line)
				}
				if strings.HasPrefix(line, "data: ") {
					t.Logf("Received SSE data: %s", line)
					// Verify the data contains expected JSON structure
					jsonData := strings.TrimPrefix(line, "data: ")
					var data map[string]interface{}
					err := json.Unmarshal([]byte(jsonData), &data)
					assert.NoError(t, err, "SSE data should be valid JSON")

					if data["status"] != nil {
						assert.Equal(t, "Connected", data["status"])
					}
				}

				// If we receive completion event, we can stop
				if eventsReceived >= 2 {
					t.Log("Received sufficient events, test completed")
					return
				}
			} else {
				// Scanner finished, check for errors
				if err := scanner.Err(); err != nil {
					t.Logf("Scanner error: %v", err)
				}
				// Final assertion for cases where we exit the loop normally
				assert.GreaterOrEqual(t, eventsReceived, 1, "Should receive at least 1 SSE event")
				return
			}
		}
	}
}
