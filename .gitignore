# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
go.work.sum

# env file
.env

# Test result output directories
cypress/screenshots/
cypress/videos/
e2e_snapshots/

# Playwright
node_modules/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Ignore e2e and frontend
cypress/
playwright-tests/
frontend/
cypress.config.js
playwright.config.ts
# news.db* # Replaced by specific entries below

# Junk / Archived Development Files
junk/

# Added/Ensured patterns from reorganization plan:

# Build Artifacts (extending existing)
# *.exe is already covered above
/bin/
/build/

# Windows reserved device name
nul

# Runtime Database Files
news.db
news.db-shm
news.db-wal

# Coverage & Test Reports (extending existing)
# *.out is already covered above
*.html
!templates/**/*.html
# /test-results/ is already covered above
# /playwright-report/ is already covered above

# IDE / OS / Project Junk (extending existing)
# /junk/ is already covered above
.DS_Store
*.log
