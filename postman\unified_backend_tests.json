{"info": {"name": "Unified Backend Tests", "description": "Comprehensive test suite for the News Filter backend API, including article creation, feedback, rescoring, and debugging functionality. Manual scoring and LLM rescoring are fully separated, with independent endpoints and strict error handling to ensure no cross-compatibility. The test plan covers all valid and invalid flows for both manual and LLM-based rescoring.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["pm.globals.set('articleSchema', {", "    type: 'object',", "    required: ['success', 'data'],", "    properties: {", "        success: { type: 'boolean' },", "        data: {", "            type: 'object',", "            required: ['article_id', 'title', 'content', 'url', 'source', 'composite_score', 'confidence'],", "            properties: {", "                article_id: { type: 'number' },", "                title: { type: 'string' },", "                content: { type: 'string' },", "                url: { type: 'string' },", "                source: { type: 'string' },", "                composite_score: { type: ['number', 'null'] },", "                confidence: { type: ['number', 'null'] }", "            }", "        }", "    }", "});", "", "pm.globals.set('errorSchema', {", "    type: 'object',", "    required: ['success', 'error'],", "    properties: {", "        success: { type: 'boolean' },", "        error: {", "            type: 'object',", "            required: ['code', 'message'],", "            properties: {", "                code: { type: 'string' },", "                message: { type: 'string' }", "            }", "        }", "    }", "});"]}}], "item": [{"name": "1. Article Management Tests", "item": [{"name": "1.1 Article Creation Tests", "item": [{"name": "Create Article - <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Article\",\n  \"content\": \"This is a test article.\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400 for missing fields\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response matches error schema\", function () {", "    const schema = pm.globals.get('errorSchema');", "    pm.response.to.have.jsonSchema(schema);", "});", "", "pm.test(\"Response contains error about missing fields\", function () {", "    var json = pm.response.json();", "    pm.expect(json.error.message).to.include('Missing required fields');", "});"], "type": "text/javascript"}}]}, {"name": "Create Article - Invalid URL Format", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Article\",\n  \"content\": \"This is a test article.\",\n  \"source\": \"test\",\n  \"url\": \"invalid-url\",\n  \"pub_date\": \"{{$isoTimestamp}}\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400 for invalid URL\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response contains error about invalid URL\", function () {", "    var json = pm.response.json();", "    pm.expect(json.error.message).to.include('Invalid URL format');", "});"], "type": "text/javascript"}}]}, {"name": "Create Article - Valid", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Article\",\n  \"content\": \"This is a test article.\",\n  \"source\": \"test\",\n  \"url\": \"https://example.com/test-{{$timestamp}}\",\n  \"pub_date\": \"{{$isoTimestamp}}\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect([200, 201]).to.include(pm.response.code);", "});", "", "pm.test(\"Response matches article schema\", function () {", "    const schema = pm.globals.get('articleSchema');", "    pm.response.to.have.jsonSchema(schema);", "});", "", "if (pm.response.code === 200 || pm.response.code === 201) {", "    var json = pm.response.json();", "    pm.environment.set(\"articleId\", json.data.article_id);", "}", "", "// Save the URL used in this request for the duplicate test", "try {", "    const requestBody = JSON.parse(pm.request.body.raw);", "    pm.environment.set(\"lastCreatedArticleURL\", requestBody.url);", "    console.log('Saved URL for duplicate test:', requestBody.url);", "} catch (e) {", "    console.error('Failed to parse request body or save URL:', e);", "}"], "type": "text/javascript"}}]}, {"name": "Create Article - Duplicate URL", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Article Duplicate\",\n  \"content\": \"This is a duplicate test article.\",\n  \"source\": \"test\",\n  \"url\": \"{{lastCreatedArticleURL}}\",\n  \"pub_date\": \"{{$isoTimestamp}}\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 409 for duplicate URL\", function () {", "    pm.response.to.have.status(409);", "});", "", "pm.test(\"Response contains error about duplicate URL\", function () {", "    var json = pm.response.json();", "    pm.expect(json.error.message).to.include('already exists');", "});"], "type": "text/javascript"}}]}]}, {"name": "1.2 Article Retrieval Tests", "item": [{"name": "Get Articles - De<PERSON>ult Parameters", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response matches schema\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.true;", "    pm.expect(responseJson.data).to.be.an('array');", "    if (responseJson.data && responseJson.data.length > 0) {", "        // Each article in the array should match article schema", "        const schema = pm.globals.get('articleSchema');", "        responseJson.data.forEach(article => {", "            pm.expect({ success: true, data: article }).to.have.jsonSchema(schema);", "        });", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Articles - With Source Filter", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles?source=test", "host": ["{{baseUrl}}"], "path": ["api", "articles"], "query": [{"key": "source", "value": "test"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response matches schema and has correct source\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.success).to.be.true;", "    pm.expect(responseJson.data).to.be.an('array');", "    if (responseJson.data && responseJson.data.length > 0) {", "        // Each article should match schema and have correct source", "        const schema = pm.globals.get('articleSchema');", "        responseJson.data.forEach(article => {", "            pm.expect({ success: true, data: article }).to.have.jsonSchema(schema);", "            pm.expect(article.source).to.equal('test');", "        });", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Articles - <PERSON><PERSON>", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles?source=test", "host": ["{{baseUrl}}"], "path": ["api", "articles"], "query": [{"key": "source", "value": "test"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains articles with correct source\", function () {", "    var json = pm.response.json();", "    pm.expect(json.success).to.be.true;", "    pm.expect(json.data).to.be.an('array');", "    if (json.data && json.data.length > 0) {", "        pm.expect(json.data[0].source).to.equal('test');", "    }", "});"], "type": "text/javascript"}}]}, {"name": "Get Ensemble Details", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles/{{articleId}}/ensemble", "host": ["{{baseUrl}}"], "path": ["api", "articles", "{{articleId}}", "ensemble"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 404\", function () {", "    pm.expect([200, 404]).to.include(pm.response.code);", "});", "", "if (pm.response.code === 404) {", "    pm.test(\"404 response matches error schema\", function () {", "        const schema = pm.globals.get('errorSchema');", "        pm.response.to.have.jsonSchema(schema);", "    });", "} else {", "    pm.test(\"200 response contains valid ensemble data\", function () {", "        const responseJson = pm.response.json();", "        pm.expect(responseJson.success).to.be.true;", "        pm.expect(responseJson.data).to.have.property('ensembles');", "        pm.expect(responseJson.data.ensembles).to.be.an('array');", "    });", "}"], "type": "text/javascript"}}]}]}]}, {"name": "2. <PERSON><PERSON><PERSON>", "item": [{"name": "Submit <PERSON> - <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/feedback", "host": ["{{baseUrl}}"], "path": ["api", "feedback"]}, "body": {"mode": "raw", "raw": "{\n  \"article_id\": {{articleId}}\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400 for missing fields\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response matches error schema\", function () {", "    const schema = pm.globals.get('errorSchema');", "    pm.response.to.have.jsonSchema(schema);", "});", "", "pm.test(\"Response contains error about missing fields\", function () {", "    var json = pm.response.json();", "    pm.expect(json.error.message).to.include('Missing required fields');", "});"], "type": "text/javascript"}}]}, {"name": "Submit <PERSON>edback - Invalid Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/feedback", "host": ["{{baseUrl}}"], "path": ["api", "feedback"]}, "body": {"mode": "raw", "raw": "{\n  \"article_id\": {{articleId}},\n  \"user_id\": \"test-user\",\n  \"feedback_text\": \"This is a test feedback.\",\n  \"category\": \"invalid-category\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400 for invalid category\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response matches error schema\", function () {", "    const schema = pm.globals.get('errorSchema');", "    pm.response.to.have.jsonSchema(schema);", "});", "", "pm.test(\"Response contains error about invalid category\", function () {", "    var json = pm.response.json();", "    pm.expect(json.error.message).to.include('Invalid feedback category');", "});"], "type": "text/javascript"}}]}, {"name": "Submit <PERSON> - <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/feedback", "host": ["{{baseUrl}}"], "path": ["api", "feedback"]}, "body": {"mode": "raw", "raw": "{\n  \"article_id\": {{articleId}},\n  \"user_id\": \"test-user\",\n  \"feedback_text\": \"This is a test feedback.\",\n  \"category\": \"agree\",\n  \"source\": \"test\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 for valid feedback\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response matches success schema\", function () {", "    pm.expect(pm.response.json()).to.have.property('success', true);", "});"], "type": "text/javascript"}}]}]}, {"name": "3. Article Rescoring Tests", "item": [{"name": "3.1 <PERSON><PERSON> Tests", "item": [{"name": "Create Article for Rescoring", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Article for Rescoring\",\n  \"content\": \"This is a test article for rescoring tests.\",\n  \"url\": \"https://example.com/rescore-{{$timestamp}}\",\n  \"pub_date\": \"{{$isoTimestamp}}\",\n  \"source\": \"test\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect([200, 201]).to.include(pm.response.code);", "});", "if (pm.response.code === 200 || pm.response.code === 201) {", "    var json = pm.response.json();", "    pm.environment.set(\"rescoreArticleId\", json.data.article_id);", "} else {", "    pm.environment.unset(\"rescoreArticleId\");", "}"], "type": "text/javascript"}}]}, {"name": "Rescore Article (Valid Score 0.5)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{rescoreArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{rescoreArticleId}}"]}, "body": {"mode": "raw", "raw": "{\n  \"score\": 0.5\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}]}, {"name": "Get Article (Verify Score)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles/{{rescoreArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "articles", "{{rescoreArticleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response matches article schema\", function () {", "    const schema = pm.globals.get('articleSchema');", "    pm.response.to.have.jsonSchema(schema);", "});", "", "pm.test(\"Article has valid composite score and confidence\", function () {", "    var json = pm.response.json();", "    pm.expect(json.data.composite_score).to.be.within(-1.0, 1.0);", "    pm.expect(json.data.confidence).to.be.within(0.0, 1.0);", "});"], "type": "text/javascript"}}]}, {"name": "Rescore Article (Upper Boundary 1.0)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{rescoreArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{rescoreArticleId}}"]}, "body": {"mode": "raw", "raw": "{\n  \"score\": 1.0\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}]}, {"name": "Rescore Article (Lower Boundary -1.0)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{rescoreArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{rescoreArticleId}}"]}, "body": {"mode": "raw", "raw": "{\n  \"score\": -1.0\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}]}]}, {"name": "3.2 Invalid Rescoring Tests", "item": [{"name": "Rescore Non-existent Article", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/999999", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "999999"]}, "body": {"mode": "raw", "raw": "{\n  \"score\": 0.5\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test(\"Response matches error schema\", function () {", "    const schema = pm.globals.get('errorSchema');", "    pm.response.to.have.jsonSchema(schema);", "});", "", "pm.test(\"Error message indicates article not found\", function () {", "    var json = pm.response.json();", "    pm.expect(json.error.message.toLowerCase()).to.include('article not found');", "});"], "type": "text/javascript"}}]}, {"name": "Create Article for Invalid Score Tests", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Test Article for Invalid Score\",\n  \"content\": \"This is a test article for invalid score tests.\",\n  \"url\": \"https://example.com/invalid-score-{{$timestamp}}\",\n  \"pub_date\": \"{{$isoTimestamp}}\",\n  \"source\": \"test\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect([200, 201]).to.include(pm.response.code);", "});", "if (pm.response.code === 200 || pm.response.code === 201) {", "    var json = pm.response.json();", "    pm.environment.set(\"invalidScoreArticleId\", json.data.article_id);", "} else {", "    pm.environment.unset(\"invalidScoreArticleId\");", "}"], "type": "text/javascript"}}]}, {"name": "Rescore Article (Below Range -2.0)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{invalidScoreArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{invalidScoreArticleId}}"]}, "body": {"mode": "raw", "raw": "{\n  \"score\": -2.0\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response matches error schema\", function () {", "    const schema = pm.globals.get('errorSchema');", "    pm.response.to.have.jsonSchema(schema);", "});", "", "pm.test(\"Error message indicates score out of range\", function () {", "    var json = pm.response.json();", "    pm.expect(json.error.message.toLowerCase()).to.include('score must be between -1.0 and 1.0');", "});"], "type": "text/javascript"}}]}, {"name": "Rescore Article (Above Range 1.1)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{invalidScoreArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{invalidScoreArticleId}}"]}, "body": {"mode": "raw", "raw": "{\n  \"score\": 1.1\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response matches error schema\", function () {", "    const schema = pm.globals.get('errorSchema');", "    pm.response.to.have.jsonSchema(schema);", "});", "", "pm.test(\"Error message indicates score out of range\", function () {", "    var json = pm.response.json();", "    pm.expect(json.error.message.toLowerCase()).to.include('score must be between -1.0 and 1.0');", "});"], "type": "text/javascript"}}]}, {"name": "Rescore Article (Non-Numeric Score)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{invalidScoreArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{invalidScoreArticleId}}"]}, "body": {"mode": "raw", "raw": "{\n  \"score\": \"not-a-number\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response matches error schema\", function () {", "    const schema = pm.globals.get('errorSchema');", "    pm.response.to.have.jsonSchema(schema);", "});", "", "pm.test(\"Error message indicates invalid score type\", function () {", "    var json = pm.response.json();", "    pm.expect(json.error.message.toLowerCase()).to.include('invalid score value');", "});"], "type": "text/javascript"}}]}, {"name": "Rescore Article (Empty JSON)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{invalidScoreArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{invalidScoreArticleId}}"]}, "body": {"mode": "raw", "raw": "{}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 400\", function () {", "    pm.expect([200, 400]).to.include(pm.response.code);", "});"], "type": "text/javascript"}}]}]}, {"name": "3.3 Score Progress Tests", "item": [{"name": "Create Article for SSE Testing", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"SSE Test Article\",\n  \"content\": \"Test content for SSE progress monitoring.\",\n  \"url\": \"https://example.com/sse-test-{{$timestamp}}\",\n  \"pub_date\": \"{{$isoTimestamp}}\",\n  \"source\": \"test\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect([200, 201]).to.include(pm.response.code);", "});", "if (pm.response.code === 200 || pm.response.code === 201) {", "    var json = pm.response.json();", "    pm.environment.set(\"sseArticleId\", json.data.article_id);", "} else {", "    pm.environment.unset(\"sseArticleId\");", "}"], "type": "text/javascript"}}]}, {"name": "<PERSON><PERSON> Rescoring for SSE Monitoring", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{sseArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{sseArticleId}}"]}, "body": {"mode": "raw", "raw": "{}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Rescoring initiated\", function () {", "    pm.expect([200, 202]).to.include(pm.response.code);", "});"], "type": "text/javascript"}}]}, {"name": "Get Score Progress (Polling)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles/{{sseArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "articles", "{{sseArticleId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Score progress monitoring (polling)\", function () {", "    const maxAttempts = 5;", "    const interval = 1000;", "    let attempts = 0;", "", "    function checkProgress() {", "        if (attempts >= maxAttempts) {", "            pm.expect.fail('Score progress monitoring timed out');", "            return;", "        }", "        attempts++;", "", "        pm.sendRequest({", "            url: pm.request.url.toString(),", "            method: 'GET'", "        }, function (err, res) {", "            if (err) {", "                pm.expect.fail(err);", "                return;", "            }", "", "            // Validate response against schema", "            const schema = pm.globals.get('articleSchema');", "            pm.expect(res.json()).to.have.jsonSchema(schema);", "", "            const json = res.json();", "            if (json.data.article.Status === 'completed') {", "                pm.expect(json.data.article.composite_score).to.not.be.undefined;", "                pm.expect(json.data.article.confidence).to.not.be.undefined;", "                return;", "            } else if (json.data.article.Status === 'error') {", "                pm.test('Error status includes error details', function() {", "                    pm.expect(json.data.article.ErrorDetails).to.not.be.undefined;", "                });", "                return;", "            }", "", "            // Continue polling if still in progress", "            setTimeout(checkProgress, interval);", "        });", "    }", "", "    checkProgress();", "});"], "type": "text/javascript"}}]}]}]}, {"name": "4. Confidence Calculation Tests", "item": [{"name": "4.1 Metadata Parsing Tests", "item": [{"name": "Create Article with <PERSON><PERSON> Confidence", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Confidence Test Article\",\n  \"content\": \"Test article for confidence calculation.\",\n  \"url\": \"https://example.com/confidence-test-{{$timestamp}}\",\n  \"pub_date\": \"{{$isoTimestamp}}\",\n  \"source\": \"test\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect([200, 201]).to.include(pm.response.code);", "});", "", "if (pm.response.code === 200 || pm.response.code === 201) {", "    var json = pm.response.json();", "    pm.environment.set(\"confidenceArticleId\", json.data.article_id);", "}"], "type": "text/javascript"}}]}, {"name": "Test Integer Confidence", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{confidenceArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{confidenceArticleId}}"]}, "body": {"mode": "raw", "raw": "{\n  \"metadata\": {\"confidence\": 1}\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// The reanalyze endpoint queues the task. Actual confidence check would require a subsequent GET and is complex with NO_AUTO_ANALYZE.", "// For now, we ensure the queueing worked (200 OK). Future tests could verify actual reanalysis if NO_AUTO_ANALYZE is false.", "// pm.test(\"Integer confidence is converted to float\", function () {", "//     var json = pm.response.json();", "//     pm.expect(json.data.confidence).to.equal(1.0);", "// });"], "type": "text/javascript"}}]}, {"name": "Test Invalid Confidence Types", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{confidenceArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{confidenceArticleId}}"]}, "body": {"mode": "raw", "raw": "{\n  \"metadata\": {\"confidence\": \"invalid\"}\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// The reanalyze endpoint queues the task. Actual confidence check would require a subsequent GET and is complex with NO_AUTO_ANALYZE.", "// For now, we ensure the queueing worked (200 OK). Future tests could verify actual reanalysis if NO_AUTO_ANALYZE is false.", "// pm.test(\"Confidence is averaged correctly\", function () {", "//     var json = pm.response.json();", "//     pm.expect(json.data.Confidence).to.equal(0.8);", "// });"], "type": "text/javascript"}}]}]}]}, {"name": "5. <PERSON><PERSON>", "item": [{"name": "5.1 <PERSON><PERSON> Tests", "item": [{"name": "First Request (<PERSON><PERSON>)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles?source=test&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "articles"], "query": [{"key": "source", "value": "test"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"First request succeeds\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Store response time for comparison with cache hit", "pm.environment.set('responseTimeMiss', pm.response.responseTime);", "", "// Store response data for comparison", "pm.environment.set('firstResponseData', JSON.stringify(pm.response.json()));"], "type": "text/javascript"}}]}, {"name": "Second Request (<PERSON><PERSON>)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles?source=test&limit=10", "host": ["{{baseUrl}}"], "path": ["api", "articles"], "query": [{"key": "source", "value": "test"}, {"key": "limit", "value": "10"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Second request succeeds\", function () {", "    pm.response.to.have.status(200);", "});", "", "const responseTimeHit = pm.response.responseTime;", "const responseTimeMiss = parseInt(pm.environment.get(\"responseTimeMiss\"));", "", "pm.test(\"Cache hit is generally not slower than cache miss\", function () {", "    pm.expect(responseTimeHit).to.be.at.most(responseTimeMiss + 15); // Allow some leeway for jitter, e.g., 15ms", "});", "", "pm.test(\"Cache returns consistent data\", function () {", "    const firstResponseData = pm.environment.get('firstResponseData');", "    pm.expect(JSON.stringify(pm.response.json())).to.equal(firstResponseData);", "});"], "type": "text/javascript"}}]}]}, {"name": "5.2 Cache Invalidation Tests", "item": [{"name": "Create Article for Cache Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/articles", "host": ["{{baseUrl}}"], "path": ["api", "articles"]}, "body": {"mode": "raw", "raw": "{\n  \"title\": \"Cache Test Article\",\n  \"content\": \"Testing cache invalidation.\",\n  \"url\": \"https://example.com/cache-test-{{$timestamp}}\",\n  \"pub_date\": \"{{$isoTimestamp}}\",\n  \"source\": \"test\"\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Article created successfully\", function () {", "    pm.expect([200, 201]).to.include(pm.response.code);", "});", "", "if (pm.response.code === 200 || pm.response.code === 201) {", "    var json = pm.response.json();", "    pm.environment.set(\"cacheTestArticleId\", json.data.article_id);", "}"], "type": "text/javascript"}}]}, {"name": "Get Articles (Pre-Update Cache)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles?source=test", "host": ["{{baseUrl}}"], "path": ["api", "articles"], "query": [{"key": "source", "value": "test"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Pre-update request succeeds\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Store pre-update state", "pm.environment.set('preUpdateData', JSON.stringify(pm.response.json()));"], "type": "text/javascript"}}]}, {"name": "Update Article Score", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/api/llm/reanalyze/{{cacheTestArticleId}}", "host": ["{{baseUrl}}"], "path": ["api", "llm", "reanalyze", "{{cacheTestArticleId}}"]}, "body": {"mode": "raw", "raw": "{\n  \"score\": 0.5\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Update succeeds\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}]}, {"name": "Get Articles (Post-Update Cache)", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/api/articles?source=test", "host": ["{{baseUrl}}"], "path": ["api", "articles"], "query": [{"key": "source", "value": "test"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Post-update request succeeds\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Verify cache was invalidated", "pm.test(\"<PERSON><PERSON> was invalidated after update\", function () {", "    const preUpdateData = pm.environment.get('preUpdateData');", "    pm.expect(JSON.stringify(pm.response.json())).to.not.equal(preUpdateData);", "});"], "type": "text/javascript"}}]}]}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080"}]}