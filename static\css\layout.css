/* Layout Helpers */
/* Layout utilities like grid, container widths, responsiveness */

/* Container for centering content */
.container {
  max-width: var(--container-max-width, 1200px);
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

/* Container variants */
.container-fluid {
  max-width: none;
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

.container-sm {
  max-width: 576px;
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

.container-md {
  max-width: 768px;
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

.container-lg {
  max-width: 992px;
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

.container-xl {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4, 1rem);
  width: 100%;
}

/* Grid Layouts */

/* Articles Grid - responsive grid for article listings */
.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-6, 1.5rem);
}

/* Flexbox fallback for browsers that don't support grid */
@supports not (display: grid) {
  .articles-grid {
    display: flex;
    flex-wrap: wrap;
    margin: -0.75rem;
  }

  .articles-grid > * {
    flex: 1 1 300px;
    margin: 0.75rem;
    max-width: calc(33.333% - 1.5rem);
  }
}

/* Responsive breakpoint for mobile */
@media screen and (max-width: 736px) {
  .articles-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4, 1rem);
  }

  @supports not (display: grid) {
    .articles-grid {
      margin: -0.5rem;
    }

    .articles-grid > * {
      flex: 1 1 100%;
      margin: 0.5rem;
      max-width: calc(100% - 1rem);
    }
  }
}

/* Two-Column Layout - for main content and sidebar */
/* Flexbox fallback for IE11 and older browsers */
.two-column-layout {
  display: flex;
  gap: var(--space-6, 1.5rem);
}

.two-column-layout > *:first-child {
  flex: 3;
  min-width: 0; /* Prevent flex item from overflowing */
}

.two-column-layout > *:last-child {
  flex: 1;
  min-width: 0; /* Prevent flex item from overflowing */
}

/* Modern grid layout for browsers that support it */
@supports (display: grid) {
  .two-column-layout {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: var(--space-6, 1.5rem);
  }

  .two-column-layout > * {
    margin-bottom: 0;
  }
}

/* Responsive breakpoint for mobile - stack vertically */
@media screen and (max-width: 768px) {
  .two-column-layout {
    flex-direction: column;
    gap: var(--space-4, 1rem);
  }

  .two-column-layout > * {
    flex: none;
  }

  @supports (display: grid) {
    .two-column-layout {
      grid-template-columns: 1fr;
      gap: var(--space-4, 1rem);
    }
  }
}

/* Equal Columns Layout - for dashboard cards and equal-width content */
/* Flexbox fallback for IE11 and older browsers */
.equal-columns-layout {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-6, 1.5rem);
}

.equal-columns-layout > * {
  flex: 1 1 45%;
  margin-bottom: var(--space-6, 1.5rem);
}

/* Modern grid layout for browsers that support it */
@supports (display: grid) {
  .equal-columns-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6, 1.5rem);
  }

  .equal-columns-layout > * {
    margin-bottom: 0;
  }
}

/* Responsive breakpoint for mobile - stack vertically */
@media screen and (max-width: 768px) {
  .equal-columns-layout {
    gap: var(--space-4, 1rem);
  }

  .equal-columns-layout > * {
    flex: 1 0 100%;
    margin-bottom: var(--space-4, 1rem);
  }

  @supports (display: grid) {
    .equal-columns-layout {
      grid-template-columns: 1fr;
      gap: var(--space-4, 1rem);
    }

    .equal-columns-layout > * {
      margin-bottom: 0;
    }
  }
}
