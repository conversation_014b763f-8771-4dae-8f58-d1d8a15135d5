{"name": "newbalancer_go", "version": "1.0.0", "main": "index.js", "scripts": {"test": "scripts/test.cmd", "test:backend": "scripts/test.cmd backend", "test:all": "scripts/test.cmd all", "test:debug": "scripts/test.cmd debug", "test:report": "scripts/test.cmd report", "test:clean": "scripts/test.cmd clean", "test:analyze": "scripts/test.cmd analyze", "test:list": "scripts/test.cmd list", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report", "test:e2e:ci": "playwright test tests/e2e/accessibility-pages.spec.ts tests/progress-indicator.spec.ts --reporter=dot", "test:accessibility": "playwright test tests/e2e/accessibility-pages.spec.ts --reporter=dot", "test:progress-indicator": "playwright test tests/progress-indicator.spec.ts --reporter=dot", "lint:css": "stylelint \"static/css/**/*.css\""}, "repository": {"type": "git", "url": "git+https://github.com/alexand<PERSON>-sa<PERSON><PERSON>/BalancedNewsGo.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/alexand<PERSON>-sa<PERSON><PERSON>/BalancedNewsGo/issues"}, "homepage": "https://github.com/alexand<PERSON>-sa<PERSON><PERSON>/BalancedNewsGo#readme", "description": "", "devDependencies": {"@axe-core/playwright": "^4.10.2", "@lhci/cli": "^0.15.1", "@playwright/test": "^1.53.1", "@stoplight/spectral-cli": "^6.11.0", "@types/eventsource": "^1.1.15", "@types/node": "^22.14.1", "eventsource": "^3.0.7", "jest-environment-jsdom": "^30.0.0", "jsdom": "^26.1.0", "newman": "^6.2.0", "stylelint": "^16.21.0", "stylelint-config-standard": "^38.0.0", "typescript": "^5.8.3", "undici-types": "^7.10.0"}, "dependencies": {"dotenv": "^16.5.0", "node-fetch": "^3.3.2", "xml2js": "^0.6.2"}}